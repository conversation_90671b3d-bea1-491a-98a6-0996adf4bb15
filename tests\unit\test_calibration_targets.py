"""
校准目标管理系统单元测试
"""

import pytest
import tempfile
import json
import pandas as pd
from pathlib import Path
from datetime import datetime

from src.calibration.targets import (
    CalibrationTargets, CalibrationTarget, TargetType, DataQuality, TargetMetadata
)


class TestCalibrationTarget:
    """测试CalibrationTarget数据类"""
    
    def test_calibration_target_creation(self):
        """测试校准目标创建"""
        target = CalibrationTarget(
            target_type=TargetType.ADENOMA_PREVALENCE,
            age_group="50-54",
            gender="male",
            value=0.15,
            standard_error=0.02,
            confidence_interval=(0.11, 0.19),
            sample_size=1000,
            data_source="Test Study",
            publication_year=2023,
            data_quality=DataQuality.HIGH,
            weight=1.5,
            priority=1
        )
        
        assert target.target_type == TargetType.ADENOMA_PREVALENCE
        assert target.age_group == "50-54"
        assert target.gender == "male"
        assert target.value == 0.15
        assert target.standard_error == 0.02
        assert target.confidence_interval == (0.11, 0.19)
        assert target.sample_size == 1000
        assert target.data_source == "Test Study"
        assert target.publication_year == 2023
        assert target.data_quality == DataQuality.HIGH
        assert target.weight == 1.5
        assert target.priority == 1
    
    def test_target_to_dict(self):
        """测试目标转换为字典"""
        target = CalibrationTarget(
            target_type=TargetType.CANCER_INCIDENCE,
            age_group="60-64",
            gender="female",
            value=0.005
        )
        
        target_dict = target.to_dict()
        
        assert target_dict['target_type'] == 'cancer_incidence'
        assert target_dict['age_group'] == '60-64'
        assert target_dict['gender'] == 'female'
        assert target_dict['value'] == 0.005
        assert target_dict['data_quality'] == 'medium'
    
    def test_target_from_dict(self):
        """测试从字典创建目标"""
        data = {
            'target_type': 'cancer_mortality',
            'age_group': '70-74',
            'gender': 'male',
            'value': 0.002,
            'standard_error': 0.0005,
            'confidence_interval': [0.001, 0.003],
            'data_quality': 'high'
        }
        
        target = CalibrationTarget.from_dict(data)
        
        assert target.target_type == TargetType.CANCER_MORTALITY
        assert target.age_group == '70-74'
        assert target.gender == 'male'
        assert target.value == 0.002
        assert target.standard_error == 0.0005
        assert target.confidence_interval == (0.001, 0.003)
        assert target.data_quality == DataQuality.HIGH


class TestCalibrationTargets:
    """测试CalibrationTargets管理系统"""
    
    @pytest.fixture
    def targets_manager(self):
        """创建临时目标管理器"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = CalibrationTargets(storage_path=temp_dir)
            yield manager
    
    @pytest.fixture
    def sample_targets(self):
        """创建样本目标数据"""
        targets = [
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="male",
                value=0.15,
                standard_error=0.02,
                data_quality=DataQuality.HIGH
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="female",
                value=0.12,
                standard_error=0.018,
                data_quality=DataQuality.HIGH
            ),
            CalibrationTarget(
                target_type=TargetType.CANCER_INCIDENCE,
                age_group="60-64",
                gender="male",
                value=0.008,
                standard_error=0.001,
                data_quality=DataQuality.MEDIUM
            )
        ]
        return targets
    
    def test_add_target(self, targets_manager, sample_targets):
        """测试添加目标"""
        target = sample_targets[0]
        targets_manager.add_target(target)
        
        retrieved = targets_manager.get_target(
            TargetType.ADENOMA_PREVALENCE, "50-54", "male"
        )
        
        assert retrieved is not None
        assert retrieved.value == 0.15
        assert retrieved.standard_error == 0.02
        assert targets_manager.metadata.total_targets == 1
    
    def test_get_targets_by_type(self, targets_manager, sample_targets):
        """测试按类型获取目标"""
        for target in sample_targets:
            targets_manager.add_target(target)
        
        adenoma_targets = targets_manager.get_targets_by_type(TargetType.ADENOMA_PREVALENCE)
        cancer_targets = targets_manager.get_targets_by_type(TargetType.CANCER_INCIDENCE)
        
        assert len(adenoma_targets) == 2
        assert len(cancer_targets) == 1
        assert all(t.target_type == TargetType.ADENOMA_PREVALENCE for t in adenoma_targets)
    
    def test_remove_target(self, targets_manager, sample_targets):
        """测试移除目标"""
        target = sample_targets[0]
        targets_manager.add_target(target)
        
        assert targets_manager.metadata.total_targets == 1
        
        success = targets_manager.remove_target(
            TargetType.ADENOMA_PREVALENCE, "50-54", "male"
        )
        
        assert success
        assert targets_manager.metadata.total_targets == 0
        
        retrieved = targets_manager.get_target(
            TargetType.ADENOMA_PREVALENCE, "50-54", "male"
        )
        assert retrieved is None
    
    def test_interpolate_age_specific_targets(self, targets_manager):
        """测试年龄特异性插值"""
        # 添加不同年龄组的目标
        targets = [
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="male",
                value=0.10
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="60-64",
                gender="male",
                value=0.20
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="70-74",
                gender="male",
                value=0.30
            )
        ]
        
        for target in targets:
            targets_manager.add_target(target)
        
        # 插值计算
        interpolated = targets_manager.interpolate_age_specific_targets(
            TargetType.ADENOMA_PREVALENCE, "male", [55, 65]
        )
        
        assert len(interpolated) == 2
        assert 55 in interpolated
        assert 65 in interpolated
        
        # 检查插值结果合理性
        assert 0.10 < interpolated[55] < 0.20  # 55岁应该在0.10-0.20之间
        assert 0.20 < interpolated[65] < 0.30  # 65岁应该在0.20-0.30之间
    
    def test_save_and_load_file(self, targets_manager, sample_targets):
        """测试保存和加载文件"""
        # 添加目标
        for target in sample_targets:
            targets_manager.add_target(target)
        
        # 保存到临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            targets_manager.save_to_file(temp_file)
            
            # 创建新的管理器并加载
            new_manager = CalibrationTargets()
            new_manager.load_from_file(temp_file)
            
            # 验证数据
            assert new_manager.metadata.total_targets == 3
            
            retrieved = new_manager.get_target(
                TargetType.ADENOMA_PREVALENCE, "50-54", "male"
            )
            assert retrieved is not None
            assert retrieved.value == 0.15
            
        finally:
            Path(temp_file).unlink(missing_ok=True)
    
    def test_load_from_csv(self, targets_manager):
        """测试从CSV文件加载"""
        # 创建测试CSV数据
        csv_data = pd.DataFrame([
            {
                'target_type': 'adenoma_prevalence',
                'age_group': '50-54',
                'gender': 'male',
                'value': 0.15,
                'standard_error': 0.02,
                'data_quality': 'high'
            },
            {
                'target_type': 'cancer_incidence',
                'age_group': '60-64',
                'gender': 'female',
                'value': 0.006,
                'standard_error': 0.001,
                'data_quality': 'medium'
            }
        ])
        
        # 保存到临时CSV文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            temp_file = f.name
        
        try:
            csv_data.to_csv(temp_file, index=False)
            
            # 加载数据
            count = targets_manager.load_targets_from_file(temp_file)
            
            assert count == 2
            assert targets_manager.metadata.total_targets == 2
            
            # 验证数据
            target1 = targets_manager.get_target(
                TargetType.ADENOMA_PREVALENCE, "50-54", "male"
            )
            assert target1 is not None
            assert target1.value == 0.15
            
            target2 = targets_manager.get_target(
                TargetType.CANCER_INCIDENCE, "60-64", "female"
            )
            assert target2 is not None
            assert target2.value == 0.006
            
        finally:
            Path(temp_file).unlink(missing_ok=True)
    
    def test_create_version_snapshot(self, targets_manager, sample_targets):
        """测试创建版本快照"""
        # 添加目标
        for target in sample_targets:
            targets_manager.add_target(target)
        
        # 创建版本快照
        version_id = targets_manager.create_version_snapshot("测试版本")
        
        assert version_id.startswith("v1_")
        assert len(targets_manager.version_history) == 1
        assert targets_manager.version_history[0]['version_id'] == version_id
        assert targets_manager.version_history[0]['description'] == "测试版本"
        
        # 检查版本文件是否存在
        version_file = targets_manager.storage_path / "versions" / version_id / "targets.json"
        assert version_file.exists()
    
    def test_create_backup(self, targets_manager, sample_targets):
        """测试创建备份"""
        # 添加目标
        for target in sample_targets:
            targets_manager.add_target(target)
        
        # 创建备份
        backup_path = targets_manager.create_backup("test_backup")
        
        assert Path(backup_path).exists()
        assert backup_path.endswith('.json.gz')
    
    def test_get_summary_statistics(self, targets_manager, sample_targets):
        """测试获取汇总统计"""
        # 添加目标
        for target in sample_targets:
            targets_manager.add_target(target)
        
        stats = targets_manager.get_summary_statistics()
        
        assert stats['total_targets'] == 3
        assert stats['target_types']['adenoma_prevalence'] == 2
        assert stats['target_types']['cancer_incidence'] == 1
        assert '50-54' in stats['age_groups']
        assert '60-64' in stats['age_groups']
        assert 'male' in stats['genders']
        assert 'female' in stats['genders']
    
    def test_model_gender_specific_differences(self, targets_manager):
        """测试性别特异性差异建模"""
        # 添加男女对比数据
        targets = [
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="male",
                value=0.15
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="female",
                value=0.12
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="60-64",
                gender="male",
                value=0.25
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="60-64",
                gender="female",
                value=0.20
            )
        ]
        
        for target in targets:
            targets_manager.add_target(target)
        
        # 建模性别差异
        results = targets_manager.model_gender_specific_differences(
            TargetType.ADENOMA_PREVALENCE, ["50-54", "60-64"]
        )
        
        assert 'male_values' in results
        assert 'female_values' in results
        assert 'gender_ratios' in results
        assert 'age_trends' in results
        
        assert results['male_values']['50-54'] == 0.15
        assert results['female_values']['50-54'] == 0.12
        assert results['gender_ratios']['50-54'] == 0.15 / 0.12
    
    def test_model_age_gender_interactions(self, targets_manager):
        """测试年龄性别交互效应建模"""
        # 添加足够的数据点
        targets = [
            CalibrationTarget(TargetType.ADENOMA_PREVALENCE, "50-54", "male", 0.10),
            CalibrationTarget(TargetType.ADENOMA_PREVALENCE, "50-54", "female", 0.08),
            CalibrationTarget(TargetType.ADENOMA_PREVALENCE, "60-64", "male", 0.20),
            CalibrationTarget(TargetType.ADENOMA_PREVALENCE, "60-64", "female", 0.15),
            CalibrationTarget(TargetType.ADENOMA_PREVALENCE, "70-74", "male", 0.30),
            CalibrationTarget(TargetType.ADENOMA_PREVALENCE, "70-74", "female", 0.22)
        ]
        
        for target in targets:
            targets_manager.add_target(target)
        
        # 建模交互效应
        results = targets_manager.model_age_gender_interactions(TargetType.ADENOMA_PREVALENCE)
        
        assert 'error' not in results
        assert 'coefficients' in results
        assert 'model_fit' in results
        assert 'predictions' in results
        
        coefficients = results['coefficients']
        assert 'intercept' in coefficients
        assert 'age_effect' in coefficients
        assert 'gender_effect' in coefficients
        assert 'interaction_effect' in coefficients
        
        # R²应该合理
        r_squared = results['model_fit']['r_squared']
        assert 0 <= r_squared <= 1
