"""
校准目标数据质量检查和验证模块
实现数据完整性、一致性检查和异常值检测功能
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from scipy import stats
import pandas as pd

from .targets import CalibrationTarget, TargetType, DataQuality

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """验证结果数据类"""
    target_key: str
    validation_type: str
    status: str  # "pass", "warning", "error"
    message: str
    details: Dict[str, Any]
    timestamp: datetime


@dataclass
class QualityReport:
    """质量报告数据类"""
    overall_score: float
    total_targets: int
    validation_results: List[ValidationResult]
    quality_metrics: Dict[str, float]
    recommendations: List[str]
    generated_time: datetime


class DataValidator:
    """校准目标数据验证器"""
    
    def __init__(self):
        """初始化数据验证器"""
        # 验证规则配置
        self.validation_rules = {
            'value_range': {
                'adenoma_prevalence': (0.0, 1.0),
                'cancer_incidence': (0.0, 0.01),
                'cancer_mortality': (0.0, 0.005),
                'screening_detection': (0.0, 1.0),
                'survival_rate': (0.0, 1.0)
            },
            'outlier_threshold': 3.0,  # Z-score阈值
            'missing_data_threshold': 0.1,  # 10%缺失数据阈值
            'consistency_tolerance': 0.2  # 20%一致性容忍度
        }
        
        logger.info("数据验证器初始化完成")
    
    def validate_targets(self, targets: List[CalibrationTarget]) -> QualityReport:
        """
        验证校准目标数据质量
        
        Args:
            targets: 校准目标列表
            
        Returns:
            质量报告
        """
        validation_results = []
        
        # 执行各种验证检查
        validation_results.extend(self._check_data_completeness(targets))
        validation_results.extend(self._check_value_ranges(targets))
        validation_results.extend(self._detect_outliers(targets))
        validation_results.extend(self._check_consistency(targets))
        validation_results.extend(self._validate_metadata(targets))
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(targets, validation_results)
        
        # 计算总体质量分数
        overall_score = self._calculate_overall_score(quality_metrics)
        
        # 生成改进建议
        recommendations = self._generate_recommendations(validation_results, quality_metrics)
        
        report = QualityReport(
            overall_score=overall_score,
            total_targets=len(targets),
            validation_results=validation_results,
            quality_metrics=quality_metrics,
            recommendations=recommendations,
            generated_time=datetime.now()
        )
        
        logger.info(f"数据质量验证完成，总体分数: {overall_score:.2f}")
        return report
    
    def _check_data_completeness(self, targets: List[CalibrationTarget]) -> List[ValidationResult]:
        """检查数据完整性"""
        results = []
        
        # 检查必需字段
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            
            # 检查基本字段
            if target.value is None or np.isnan(target.value):
                results.append(ValidationResult(
                    target_key=target_key,
                    validation_type="completeness",
                    status="error",
                    message="目标值缺失",
                    details={'field': 'value'},
                    timestamp=datetime.now()
                ))
            
            # 检查标准误差
            if target.standard_error is None:
                results.append(ValidationResult(
                    target_key=target_key,
                    validation_type="completeness",
                    status="warning",
                    message="标准误差缺失，可能影响权重计算",
                    details={'field': 'standard_error'},
                    timestamp=datetime.now()
                ))
            
            # 检查样本量
            if target.sample_size is None:
                results.append(ValidationResult(
                    target_key=target_key,
                    validation_type="completeness",
                    status="warning",
                    message="样本量缺失，可能影响数据质量评估",
                    details={'field': 'sample_size'},
                    timestamp=datetime.now()
                ))
        
        return results
    
    def _check_value_ranges(self, targets: List[CalibrationTarget]) -> List[ValidationResult]:
        """检查数值范围"""
        results = []
        
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            
            # 获取预期范围
            expected_range = self.validation_rules['value_range'].get(
                target.target_type.value, (0.0, float('inf'))
            )
            
            # 检查目标值范围
            if not (expected_range[0] <= target.value <= expected_range[1]):
                results.append(ValidationResult(
                    target_key=target_key,
                    validation_type="range",
                    status="error",
                    message=f"目标值 {target.value} 超出预期范围 {expected_range}",
                    details={
                        'value': target.value,
                        'expected_range': expected_range
                    },
                    timestamp=datetime.now()
                ))
            
            # 检查标准误差
            if target.standard_error is not None:
                if target.standard_error < 0:
                    results.append(ValidationResult(
                        target_key=target_key,
                        validation_type="range",
                        status="error",
                        message="标准误差不能为负数",
                        details={'standard_error': target.standard_error},
                        timestamp=datetime.now()
                    ))
                elif target.standard_error > target.value:
                    results.append(ValidationResult(
                        target_key=target_key,
                        validation_type="range",
                        status="warning",
                        message="标准误差大于目标值，数据可能不可靠",
                        details={
                            'standard_error': target.standard_error,
                            'value': target.value
                        },
                        timestamp=datetime.now()
                    ))
        
        return results
    
    def _detect_outliers(self, targets: List[CalibrationTarget]) -> List[ValidationResult]:
        """检测异常值"""
        results = []
        
        # 按目标类型分组检测异常值
        targets_by_type = {}
        for target in targets:
            if target.target_type not in targets_by_type:
                targets_by_type[target.target_type] = []
            targets_by_type[target.target_type].append(target)
        
        for target_type, type_targets in targets_by_type.items():
            if len(type_targets) < 3:  # 需要足够的数据点
                continue
            
            values = [t.value for t in type_targets]
            mean_val = np.mean(values)
            std_val = np.std(values)
            
            if std_val == 0:  # 避免除零
                continue
            
            # 计算Z-score
            for target in type_targets:
                z_score = abs(target.value - mean_val) / std_val
                target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
                
                if z_score > self.validation_rules['outlier_threshold']:
                    results.append(ValidationResult(
                        target_key=target_key,
                        validation_type="outlier",
                        status="warning",
                        message=f"可能的异常值，Z-score = {z_score:.2f}",
                        details={
                            'z_score': z_score,
                            'value': target.value,
                            'group_mean': mean_val,
                            'group_std': std_val
                        },
                        timestamp=datetime.now()
                    ))
        
        return results
    
    def _check_consistency(self, targets: List[CalibrationTarget]) -> List[ValidationResult]:
        """检查数据一致性"""
        results = []
        
        # 检查年龄组一致性（相邻年龄组的值应该相对平滑）
        targets_by_type_gender = {}
        for target in targets:
            key = f"{target.target_type.value}_{target.gender}"
            if key not in targets_by_type_gender:
                targets_by_type_gender[key] = []
            targets_by_type_gender[key].append(target)
        
        for key, group_targets in targets_by_type_gender.items():
            if len(group_targets) < 3:
                continue
            
            # 按年龄组排序
            group_targets.sort(key=lambda t: self._get_age_group_midpoint(t.age_group))
            
            # 检查相邻年龄组的变化率
            for i in range(1, len(group_targets) - 1):
                prev_target = group_targets[i - 1]
                curr_target = group_targets[i]
                next_target = group_targets[i + 1]
                
                # 计算变化率
                prev_change = abs(curr_target.value - prev_target.value) / prev_target.value
                next_change = abs(next_target.value - curr_target.value) / curr_target.value
                
                # 如果变化率差异很大，可能存在不一致
                if abs(prev_change - next_change) > self.validation_rules['consistency_tolerance']:
                    target_key = f"{curr_target.target_type.value}_{curr_target.age_group}_{curr_target.gender}"
                    results.append(ValidationResult(
                        target_key=target_key,
                        validation_type="consistency",
                        status="warning",
                        message="年龄组间数值变化不一致",
                        details={
                            'prev_change_rate': prev_change,
                            'next_change_rate': next_change,
                            'age_group': curr_target.age_group
                        },
                        timestamp=datetime.now()
                    ))
        
        return results
    
    def _validate_metadata(self, targets: List[CalibrationTarget]) -> List[ValidationResult]:
        """验证元数据"""
        results = []
        
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            
            # 检查发表年份
            current_year = datetime.now().year
            if target.publication_year > current_year:
                results.append(ValidationResult(
                    target_key=target_key,
                    validation_type="metadata",
                    status="error",
                    message="发表年份不能是未来年份",
                    details={'publication_year': target.publication_year},
                    timestamp=datetime.now()
                ))
            elif target.publication_year < 1990:
                results.append(ValidationResult(
                    target_key=target_key,
                    validation_type="metadata",
                    status="warning",
                    message="数据较旧，可能需要更新",
                    details={'publication_year': target.publication_year},
                    timestamp=datetime.now()
                ))
            
            # 检查优先级
            if not (1 <= target.priority <= 5):
                results.append(ValidationResult(
                    target_key=target_key,
                    validation_type="metadata",
                    status="warning",
                    message="优先级应在1-5范围内",
                    details={'priority': target.priority},
                    timestamp=datetime.now()
                ))
        
        return results
    
    def _get_age_group_midpoint(self, age_group: str) -> float:
        """获取年龄组中点"""
        if '-' in age_group:
            start, end = age_group.split('-')
            return (int(start) + int(end)) / 2
        elif '+' in age_group:
            start = int(age_group.replace('+', ''))
            return start + 5
        else:
            return float(age_group)
    
    def _calculate_quality_metrics(
        self, 
        targets: List[CalibrationTarget], 
        validation_results: List[ValidationResult]
    ) -> Dict[str, float]:
        """计算质量指标"""
        total_targets = len(targets)
        
        # 统计验证结果
        error_count = sum(1 for r in validation_results if r.status == "error")
        warning_count = sum(1 for r in validation_results if r.status == "warning")
        pass_count = total_targets - error_count - warning_count
        
        # 计算各种指标
        metrics = {
            'completeness_score': pass_count / total_targets if total_targets > 0 else 0,
            'error_rate': error_count / total_targets if total_targets > 0 else 0,
            'warning_rate': warning_count / total_targets if total_targets > 0 else 0,
            'data_coverage': self._calculate_coverage(targets),
            'quality_distribution': self._calculate_quality_distribution(targets)
        }
        
        return metrics
    
    def _calculate_coverage(self, targets: List[CalibrationTarget]) -> float:
        """计算数据覆盖度"""
        # 统计覆盖的目标类型、年龄组、性别组合
        combinations = set()
        for target in targets:
            combinations.add((target.target_type, target.age_group, target.gender))
        
        # 理论上的最大组合数（假设5种类型 × 5个年龄组 × 2种性别）
        max_combinations = len(TargetType) * 5 * 2
        
        return len(combinations) / max_combinations
    
    def _calculate_quality_distribution(self, targets: List[CalibrationTarget]) -> float:
        """计算质量分布分数"""
        quality_scores = {
            DataQuality.HIGH: 1.0,
            DataQuality.MEDIUM: 0.7,
            DataQuality.LOW: 0.4,
            DataQuality.UNCERTAIN: 0.1
        }
        
        if not targets:
            return 0.0
        
        total_score = sum(quality_scores.get(target.data_quality, 0.5) for target in targets)
        return total_score / len(targets)
    
    def _calculate_overall_score(self, quality_metrics: Dict[str, float]) -> float:
        """计算总体质量分数"""
        weights = {
            'completeness_score': 0.3,
            'error_rate': -0.4,  # 负权重，错误率越高分数越低
            'warning_rate': -0.2,
            'data_coverage': 0.2,
            'quality_distribution': 0.2
        }
        
        score = 0.0
        for metric, weight in weights.items():
            if metric in quality_metrics:
                score += quality_metrics[metric] * weight
        
        # 确保分数在0-1范围内
        return max(0.0, min(1.0, score))
    
    def _generate_recommendations(
        self, 
        validation_results: List[ValidationResult],
        quality_metrics: Dict[str, float]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于错误和警告生成建议
        error_types = {}
        warning_types = {}
        
        for result in validation_results:
            if result.status == "error":
                error_types[result.validation_type] = error_types.get(result.validation_type, 0) + 1
            elif result.status == "warning":
                warning_types[result.validation_type] = warning_types.get(result.validation_type, 0) + 1
        
        # 针对错误的建议
        if 'completeness' in error_types:
            recommendations.append("补充缺失的必需数据字段，特别是目标值")
        
        if 'range' in error_types:
            recommendations.append("检查并修正超出合理范围的数值")
        
        # 针对警告的建议
        if 'outlier' in warning_types:
            recommendations.append("审查可能的异常值，确认数据准确性")
        
        if 'consistency' in warning_types:
            recommendations.append("检查年龄组间数值的一致性，平滑异常变化")
        
        # 基于质量指标的建议
        if quality_metrics.get('data_coverage', 0) < 0.5:
            recommendations.append("增加数据覆盖度，补充更多年龄组和性别的数据")
        
        if quality_metrics.get('quality_distribution', 0) < 0.6:
            recommendations.append("提高数据质量，寻找更可靠的数据源")
        
        if not recommendations:
            recommendations.append("数据质量良好，建议定期更新和验证")
        
        return recommendations
