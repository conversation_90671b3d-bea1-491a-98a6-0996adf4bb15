"""
校准目标管理模块
实现校准目标数据的导入、管理、存储和版本控制功能
"""

import json
import pickle
import gzip
import hashlib
import shutil
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
import logging

import numpy as np
import pandas as pd
import yaml

logger = logging.getLogger(__name__)


class TargetType(Enum):
    """校准目标类型枚举"""
    ADENOMA_PREVALENCE = "adenoma_prevalence"
    CANCER_INCIDENCE = "cancer_incidence"
    CANCER_MORTALITY = "cancer_mortality"
    SCREENING_DETECTION = "screening_detection"
    SURVIVAL_RATE = "survival_rate"


class DataQuality(Enum):
    """数据质量等级枚举"""
    HIGH = "high"           # 高质量数据（大样本、多中心）
    MEDIUM = "medium"       # 中等质量数据（中等样本）
    LOW = "low"            # 低质量数据（小样本、单中心）
    UNCERTAIN = "uncertain" # 不确定质量


@dataclass
class CalibrationTarget:
    """校准目标数据类"""
    target_type: TargetType
    age_group: str          # "50-54", "55-59", etc.
    gender: str             # "male", "female", "both"
    value: float
    standard_error: Optional[float] = None
    confidence_interval: Optional[Tuple[float, float]] = None
    sample_size: Optional[int] = None
    data_source: str = ""
    publication_year: int = 2023
    data_quality: DataQuality = DataQuality.MEDIUM
    weight: float = 1.0
    priority: int = 1       # 1=highest, 5=lowest
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = asdict(self)
        result['target_type'] = self.target_type.value
        result['data_quality'] = self.data_quality.value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CalibrationTarget':
        """从字典创建对象"""
        data = data.copy()
        data['target_type'] = TargetType(data['target_type'])
        data['data_quality'] = DataQuality(data['data_quality'])
        
        # 处理置信区间
        if 'confidence_interval' in data and data['confidence_interval']:
            if isinstance(data['confidence_interval'], list):
                data['confidence_interval'] = tuple(data['confidence_interval'])
        
        return cls(**data)


@dataclass
class TargetMetadata:
    """目标数据元信息"""
    version: str
    created_time: datetime
    modified_time: datetime
    description: str
    author: str
    data_sources: List[str]
    total_targets: int
    checksum: str


class CalibrationTargets:
    """校准目标管理系统"""
    
    def __init__(self, storage_path: Optional[str] = None):
        """
        初始化校准目标管理系统
        
        Args:
            storage_path: 存储路径，默认为data/calibration_targets
        """
        self.storage_path = Path(storage_path or "data/calibration_targets")
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 目标数据存储结构: {target_type: {age_group: {gender: CalibrationTarget}}}
        self.targets: Dict[TargetType, Dict[str, Dict[str, CalibrationTarget]]] = {}
        
        # 元数据
        self.metadata = TargetMetadata(
            version="1.0.0",
            created_time=datetime.now(),
            modified_time=datetime.now(),
            description="校准目标数据集",
            author="",
            data_sources=[],
            total_targets=0,
            checksum=""
        )
        
        # 版本历史
        self.version_history: List[Dict[str, Any]] = []
        
        logger.info(f"校准目标管理系统初始化完成，存储路径: {self.storage_path}")
    
    def load_targets_from_file(
        self, 
        file_path: Union[str, Path], 
        file_format: str = "auto"
    ) -> int:
        """
        从文件加载校准目标
        
        Args:
            file_path: 文件路径
            file_format: 文件格式 (auto, csv, excel, json, yaml)
            
        Returns:
            加载的目标数量
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 自动检测文件格式
        if file_format == "auto":
            file_format = self._detect_file_format(file_path)
        
        logger.info(f"开始从文件加载校准目标: {file_path}, 格式: {file_format}")
        
        try:
            # 根据格式加载数据
            if file_format == "csv":
                df = pd.read_csv(file_path)
            elif file_format == "excel":
                df = pd.read_excel(file_path)
            elif file_format == "json":
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if isinstance(data, list):
                    df = pd.DataFrame(data)
                else:
                    df = pd.json_normalize(data)
            elif file_format == "yaml":
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                if isinstance(data, list):
                    df = pd.DataFrame(data)
                else:
                    df = pd.json_normalize(data)
            else:
                raise ValueError(f"不支持的文件格式: {file_format}")
            
            # 验证数据格式
            self._validate_target_data(df)
            
            # 转换为CalibrationTarget对象并添加
            loaded_count = 0
            for _, row in df.iterrows():
                target = self._create_target_from_row(row)
                self.add_target(target)
                loaded_count += 1
            
            # 更新元数据
            self.metadata.data_sources.append(str(file_path))
            self.metadata.modified_time = datetime.now()
            self._update_checksum()
            
            logger.info(f"成功加载 {loaded_count} 个校准目标")
            return loaded_count
            
        except Exception as e:
            logger.error(f"加载校准目标失败: {e}")
            raise
    
    def _detect_file_format(self, file_path: Path) -> str:
        """检测文件格式"""
        suffix = file_path.suffix.lower()
        format_map = {
            '.csv': 'csv',
            '.xlsx': 'excel',
            '.xls': 'excel',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml'
        }
        return format_map.get(suffix, 'csv')
    
    def _validate_target_data(self, df: pd.DataFrame) -> None:
        """验证目标数据格式"""
        required_columns = ['target_type', 'age_group', 'gender', 'value']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"缺少必需的列: {missing_columns}")
        
        # 验证目标类型
        invalid_types = df[~df['target_type'].isin([t.value for t in TargetType])]
        if not invalid_types.empty:
            raise ValueError(f"无效的目标类型: {invalid_types['target_type'].unique()}")
        
        # 验证数值
        if df['value'].isna().any():
            raise ValueError("目标值不能为空")
        
        logger.debug("目标数据格式验证通过")
    
    def _create_target_from_row(self, row: pd.Series) -> CalibrationTarget:
        """从数据行创建CalibrationTarget对象"""
        # 解析置信区间
        ci = None
        if 'confidence_interval' in row and pd.notna(row['confidence_interval']):
            ci_str = str(row['confidence_interval'])
            if ',' in ci_str:
                try:
                    ci_parts = ci_str.strip('()[]').split(',')
                    ci = (float(ci_parts[0]), float(ci_parts[1]))
                except (ValueError, IndexError):
                    logger.warning(f"无法解析置信区间: {ci_str}")
        
        return CalibrationTarget(
            target_type=TargetType(row['target_type']),
            age_group=row['age_group'],
            gender=row['gender'],
            value=float(row['value']),
            standard_error=row.get('standard_error') if pd.notna(row.get('standard_error')) else None,
            confidence_interval=ci,
            sample_size=int(row['sample_size']) if pd.notna(row.get('sample_size')) else None,
            data_source=row.get('data_source', ''),
            publication_year=int(row.get('publication_year', 2023)),
            data_quality=DataQuality(row.get('data_quality', 'medium')),
            weight=float(row.get('weight', 1.0)),
            priority=int(row.get('priority', 1))
        )

    def add_target(self, target: CalibrationTarget) -> None:
        """
        添加校准目标

        Args:
            target: 校准目标对象
        """
        target_type = target.target_type
        age_group = target.age_group
        gender = target.gender

        # 初始化嵌套字典结构
        if target_type not in self.targets:
            self.targets[target_type] = {}
        if age_group not in self.targets[target_type]:
            self.targets[target_type][age_group] = {}

        # 添加目标
        self.targets[target_type][age_group][gender] = target

        # 更新元数据
        self.metadata.total_targets = self._count_total_targets()
        self.metadata.modified_time = datetime.now()

        logger.debug(f"添加校准目标: {target_type.value}, {age_group}, {gender}")

    def get_target(
        self,
        target_type: TargetType,
        age_group: str,
        gender: str
    ) -> Optional[CalibrationTarget]:
        """
        获取特定的校准目标

        Args:
            target_type: 目标类型
            age_group: 年龄组
            gender: 性别

        Returns:
            校准目标对象，如果不存在则返回None
        """
        return (self.targets
                .get(target_type, {})
                .get(age_group, {})
                .get(gender))

    def get_targets_by_type(self, target_type: TargetType) -> List[CalibrationTarget]:
        """
        获取特定类型的所有校准目标

        Args:
            target_type: 目标类型

        Returns:
            校准目标列表
        """
        targets = []
        if target_type in self.targets:
            for age_group_dict in self.targets[target_type].values():
                for target in age_group_dict.values():
                    targets.append(target)
        return targets

    def get_all_targets(self) -> List[CalibrationTarget]:
        """获取所有校准目标"""
        all_targets = []
        for target_type in self.targets:
            all_targets.extend(self.get_targets_by_type(target_type))
        return all_targets

    def remove_target(
        self,
        target_type: TargetType,
        age_group: str,
        gender: str
    ) -> bool:
        """
        移除校准目标

        Args:
            target_type: 目标类型
            age_group: 年龄组
            gender: 性别

        Returns:
            是否成功移除
        """
        try:
            if (target_type in self.targets and
                age_group in self.targets[target_type] and
                gender in self.targets[target_type][age_group]):

                del self.targets[target_type][age_group][gender]

                # 清理空的字典
                if not self.targets[target_type][age_group]:
                    del self.targets[target_type][age_group]
                if not self.targets[target_type]:
                    del self.targets[target_type]

                # 更新元数据
                self.metadata.total_targets = self._count_total_targets()
                self.metadata.modified_time = datetime.now()

                logger.debug(f"移除校准目标: {target_type.value}, {age_group}, {gender}")
                return True
        except Exception as e:
            logger.error(f"移除校准目标失败: {e}")

        return False

    def interpolate_age_specific_targets(
        self,
        target_type: TargetType,
        gender: str,
        target_ages: List[int]
    ) -> Dict[int, float]:
        """
        插值计算特定年龄的目标值

        Args:
            target_type: 目标类型
            gender: 性别
            target_ages: 目标年龄列表

        Returns:
            年龄到目标值的映射
        """
        # 获取现有的年龄组数据
        existing_targets = []
        for age_group, gender_dict in self.targets.get(target_type, {}).items():
            if gender in gender_dict:
                target = gender_dict[gender]
                age_mid = self._get_age_group_midpoint(age_group)
                existing_targets.append((age_mid, target.value))

        if not existing_targets:
            logger.warning(f"没有找到 {target_type.value} {gender} 的目标数据")
            return {}

        # 排序
        existing_targets.sort(key=lambda x: x[0])
        ages, values = zip(*existing_targets)

        # 线性插值
        interpolated_values = {}
        for target_age in target_ages:
            if target_age <= ages[0]:
                # 外推到最小年龄以下
                interpolated_values[target_age] = values[0]
            elif target_age >= ages[-1]:
                # 外推到最大年龄以上
                interpolated_values[target_age] = values[-1]
            else:
                # 线性插值
                for i in range(len(ages) - 1):
                    if ages[i] <= target_age <= ages[i + 1]:
                        # 线性插值公式
                        t = (target_age - ages[i]) / (ages[i + 1] - ages[i])
                        interpolated_value = values[i] + t * (values[i + 1] - values[i])
                        interpolated_values[target_age] = interpolated_value
                        break

        logger.debug(f"为 {len(target_ages)} 个年龄插值计算目标值")
        return interpolated_values

    def _get_age_group_midpoint(self, age_group: str) -> float:
        """获取年龄组中点"""
        if '-' in age_group:
            start, end = age_group.split('-')
            return (int(start) + int(end)) / 2
        elif '+' in age_group:
            start = int(age_group.replace('+', ''))
            return start + 5  # 假设开放区间的中点
        else:
            return float(age_group)

    def _count_total_targets(self) -> int:
        """计算总目标数量"""
        count = 0
        for target_type_dict in self.targets.values():
            for age_group_dict in target_type_dict.values():
                count += len(age_group_dict)
        return count

    def _update_checksum(self) -> None:
        """更新数据校验和"""
        # 创建所有目标的哈希
        all_targets_data = []
        for target in self.get_all_targets():
            all_targets_data.append(target.to_dict())

        # 排序确保一致性
        all_targets_data.sort(key=lambda x: (x['target_type'], x['age_group'], x['gender']))

        # 计算MD5哈希
        data_str = json.dumps(all_targets_data, sort_keys=True)
        self.metadata.checksum = hashlib.md5(data_str.encode()).hexdigest()

    def save_to_file(self, file_path: Optional[Union[str, Path]] = None) -> None:
        """
        保存校准目标到文件

        Args:
            file_path: 保存路径，默认使用存储路径下的targets.json
        """
        if file_path is None:
            file_path = self.storage_path / "targets.json"
        else:
            file_path = Path(file_path)

        # 准备保存数据
        save_data = {
            'metadata': {
                'version': self.metadata.version,
                'created_time': self.metadata.created_time.isoformat(),
                'modified_time': self.metadata.modified_time.isoformat(),
                'description': self.metadata.description,
                'author': self.metadata.author,
                'data_sources': self.metadata.data_sources,
                'total_targets': self.metadata.total_targets,
                'checksum': self.metadata.checksum
            },
            'targets': [target.to_dict() for target in self.get_all_targets()]
        }

        # 保存到文件
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        logger.info(f"校准目标已保存到: {file_path}")

    def load_from_file(self, file_path: Union[str, Path]) -> None:
        """
        从文件加载校准目标

        Args:
            file_path: 文件路径
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 加载元数据
        metadata = data.get('metadata', {})
        self.metadata = TargetMetadata(
            version=metadata.get('version', '1.0.0'),
            created_time=datetime.fromisoformat(metadata.get('created_time', datetime.now().isoformat())),
            modified_time=datetime.fromisoformat(metadata.get('modified_time', datetime.now().isoformat())),
            description=metadata.get('description', ''),
            author=metadata.get('author', ''),
            data_sources=metadata.get('data_sources', []),
            total_targets=metadata.get('total_targets', 0),
            checksum=metadata.get('checksum', '')
        )

        # 清空现有目标
        self.targets.clear()

        # 加载目标数据
        targets_data = data.get('targets', [])
        for target_dict in targets_data:
            target = CalibrationTarget.from_dict(target_dict)
            self.add_target(target)

        logger.info(f"从文件加载了 {len(targets_data)} 个校准目标")

    def create_version_snapshot(self, description: str = "") -> str:
        """
        创建版本快照

        Args:
            description: 版本描述

        Returns:
            版本ID
        """
        version_id = f"v{len(self.version_history) + 1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 创建版本目录
        version_dir = self.storage_path / "versions" / version_id
        version_dir.mkdir(parents=True, exist_ok=True)

        # 保存当前状态
        self.save_to_file(version_dir / "targets.json")

        # 记录版本信息
        version_info = {
            'version_id': version_id,
            'timestamp': datetime.now().isoformat(),
            'description': description,
            'total_targets': self.metadata.total_targets,
            'checksum': self.metadata.checksum
        }

        self.version_history.append(version_info)

        # 保存版本历史
        with open(self.storage_path / "version_history.json", 'w', encoding='utf-8') as f:
            json.dump(self.version_history, f, indent=2, ensure_ascii=False)

        logger.info(f"创建版本快照: {version_id}")
        return version_id

    def restore_from_version(self, version_id: str) -> None:
        """
        从版本恢复

        Args:
            version_id: 版本ID
        """
        version_file = self.storage_path / "versions" / version_id / "targets.json"

        if not version_file.exists():
            raise FileNotFoundError(f"版本文件不存在: {version_file}")

        # 创建当前状态的备份
        backup_id = self.create_version_snapshot(f"恢复前备份 - {datetime.now()}")

        # 从版本文件恢复
        self.load_from_file(version_file)

        logger.info(f"从版本 {version_id} 恢复完成，备份ID: {backup_id}")

    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """
        创建备份

        Args:
            backup_name: 备份名称

        Returns:
            备份路径
        """
        if backup_name is None:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        backup_dir = self.storage_path / "backups" / backup_name
        backup_dir.mkdir(parents=True, exist_ok=True)

        # 保存压缩备份
        backup_file = backup_dir / "targets_backup.json.gz"

        # 准备备份数据
        metadata_dict = asdict(self.metadata)
        # 转换datetime对象为ISO格式字符串
        metadata_dict['created_time'] = self.metadata.created_time.isoformat()
        metadata_dict['modified_time'] = self.metadata.modified_time.isoformat()

        backup_data = {
            'metadata': metadata_dict,
            'targets': [target.to_dict() for target in self.get_all_targets()],
            'version_history': self.version_history,
            'backup_timestamp': datetime.now().isoformat()
        }

        # 压缩保存
        with gzip.open(backup_file, 'wt', encoding='utf-8') as f:
            json.dump(backup_data, f, indent=2, ensure_ascii=False)

        logger.info(f"创建备份: {backup_file}")
        return str(backup_file)

    def restore_from_backup(self, backup_path: Union[str, Path]) -> None:
        """
        从备份恢复

        Args:
            backup_path: 备份文件路径
        """
        backup_path = Path(backup_path)

        if not backup_path.exists():
            raise FileNotFoundError(f"备份文件不存在: {backup_path}")

        # 读取压缩备份
        with gzip.open(backup_path, 'rt', encoding='utf-8') as f:
            backup_data = json.load(f)

        # 恢复元数据
        metadata = backup_data.get('metadata', {})
        # 转换ISO格式字符串为datetime对象
        if 'created_time' in metadata and isinstance(metadata['created_time'], str):
            metadata['created_time'] = datetime.fromisoformat(metadata['created_time'])
        if 'modified_time' in metadata and isinstance(metadata['modified_time'], str):
            metadata['modified_time'] = datetime.fromisoformat(metadata['modified_time'])

        self.metadata = TargetMetadata(**metadata)

        # 恢复版本历史
        self.version_history = backup_data.get('version_history', [])

        # 清空并恢复目标数据
        self.targets.clear()
        targets_data = backup_data.get('targets', [])
        for target_dict in targets_data:
            target = CalibrationTarget.from_dict(target_dict)
            self.add_target(target)

        logger.info(f"从备份恢复了 {len(targets_data)} 个校准目标")

    def get_summary_statistics(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        stats = {
            'total_targets': self.metadata.total_targets,
            'target_types': {},
            'age_groups': set(),
            'genders': set(),
            'data_quality_distribution': {},
            'data_sources': len(self.metadata.data_sources),
            'version': self.metadata.version,
            'last_modified': self.metadata.modified_time.isoformat()
        }

        # 统计各类型目标数量
        for target_type in TargetType:
            targets = self.get_targets_by_type(target_type)
            stats['target_types'][target_type.value] = len(targets)

        # 统计年龄组、性别和数据质量分布
        quality_counts = {q.value: 0 for q in DataQuality}

        for target in self.get_all_targets():
            stats['age_groups'].add(target.age_group)
            stats['genders'].add(target.gender)
            quality_counts[target.data_quality.value] += 1

        stats['age_groups'] = sorted(list(stats['age_groups']))
        stats['genders'] = sorted(list(stats['genders']))
        stats['data_quality_distribution'] = quality_counts

        return stats

    def model_gender_specific_differences(
        self,
        target_type: TargetType,
        age_groups: List[str]
    ) -> Dict[str, Dict[str, float]]:
        """
        建模性别特异性差异

        Args:
            target_type: 目标类型
            age_groups: 年龄组列表

        Returns:
            性别差异模型结果
        """
        results = {
            'male_values': {},
            'female_values': {},
            'gender_ratios': {},
            'age_trends': {}
        }

        for age_group in age_groups:
            male_target = self.get_target(target_type, age_group, "male")
            female_target = self.get_target(target_type, age_group, "female")

            if male_target and female_target:
                results['male_values'][age_group] = male_target.value
                results['female_values'][age_group] = female_target.value

                # 计算性别比率（男性/女性）
                if female_target.value > 0:
                    ratio = male_target.value / female_target.value
                    results['gender_ratios'][age_group] = ratio

        # 分析年龄趋势
        if results['male_values']:
            male_ages = [self._get_age_group_midpoint(ag) for ag in results['male_values'].keys()]
            male_values = list(results['male_values'].values())

            # 简单线性趋势
            if len(male_ages) > 1:
                male_trend = np.polyfit(male_ages, male_values, 1)[0]
                results['age_trends']['male_slope'] = male_trend

        if results['female_values']:
            female_ages = [self._get_age_group_midpoint(ag) for ag in results['female_values'].keys()]
            female_values = list(results['female_values'].values())

            if len(female_ages) > 1:
                female_trend = np.polyfit(female_ages, female_values, 1)[0]
                results['age_trends']['female_slope'] = female_trend

        logger.debug(f"完成性别特异性差异建模: {target_type.value}")
        return results

    def model_age_gender_interactions(
        self,
        target_type: TargetType
    ) -> Dict[str, Any]:
        """
        建模年龄性别交互效应

        Args:
            target_type: 目标类型

        Returns:
            交互效应模型结果
        """
        # 收集所有数据点
        data_points = []

        for age_group, gender_dict in self.targets.get(target_type, {}).items():
            age_mid = self._get_age_group_midpoint(age_group)

            for gender, target in gender_dict.items():
                gender_code = 1 if gender == "male" else 0
                data_points.append({
                    'age': age_mid,
                    'gender': gender_code,
                    'value': target.value,
                    'age_group': age_group,
                    'gender_name': gender
                })

        if len(data_points) < 4:  # 需要足够的数据点
            logger.warning(f"数据点不足，无法建模交互效应: {len(data_points)}")
            return {'error': '数据点不足'}

        # 转换为数组
        ages = np.array([dp['age'] for dp in data_points])
        genders = np.array([dp['gender'] for dp in data_points])
        values = np.array([dp['value'] for dp in data_points])

        # 构建设计矩阵 [1, age, gender, age*gender]
        X = np.column_stack([
            np.ones(len(ages)),  # 截距
            ages,                # 年龄主效应
            genders,             # 性别主效应
            ages * genders       # 年龄性别交互效应
        ])

        try:
            # 最小二乘拟合
            coefficients = np.linalg.lstsq(X, values, rcond=None)[0]

            # 计算拟合值和残差
            fitted_values = X @ coefficients
            residuals = values - fitted_values

            # 计算R²
            ss_res = np.sum(residuals ** 2)
            ss_tot = np.sum((values - np.mean(values)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

            results = {
                'coefficients': {
                    'intercept': coefficients[0],
                    'age_effect': coefficients[1],
                    'gender_effect': coefficients[2],
                    'interaction_effect': coefficients[3]
                },
                'model_fit': {
                    'r_squared': r_squared,
                    'rmse': np.sqrt(np.mean(residuals ** 2))
                },
                'predictions': {}
            }

            # 为每个数据点生成预测
            for i, dp in enumerate(data_points):
                key = f"{dp['age_group']}_{dp['gender_name']}"
                results['predictions'][key] = {
                    'observed': dp['value'],
                    'predicted': fitted_values[i],
                    'residual': residuals[i]
                }

            logger.debug(f"完成年龄性别交互效应建模: {target_type.value}, R² = {r_squared:.3f}")
            return results

        except np.linalg.LinAlgError as e:
            logger.error(f"交互效应建模失败: {e}")
            return {'error': str(e)}

    def adjust_for_population_specificity(
        self,
        target_type: TargetType,
        population_adjustments: Dict[str, float]
    ) -> Dict[str, CalibrationTarget]:
        """
        根据人群特异性调整目标值

        Args:
            target_type: 目标类型
            population_adjustments: 人群调整因子

        Returns:
            调整后的目标字典
        """
        adjusted_targets = {}

        for age_group, gender_dict in self.targets.get(target_type, {}).items():
            for gender, target in gender_dict.items():
                # 获取调整因子
                adjustment_key = f"{age_group}_{gender}"
                adjustment_factor = population_adjustments.get(
                    adjustment_key,
                    population_adjustments.get(gender, 1.0)
                )

                # 创建调整后的目标
                adjusted_target = CalibrationTarget(
                    target_type=target.target_type,
                    age_group=target.age_group,
                    gender=target.gender,
                    value=target.value * adjustment_factor,
                    standard_error=target.standard_error * adjustment_factor if target.standard_error else None,
                    confidence_interval=(
                        (target.confidence_interval[0] * adjustment_factor,
                         target.confidence_interval[1] * adjustment_factor)
                        if target.confidence_interval else None
                    ),
                    sample_size=target.sample_size,
                    data_source=f"{target.data_source} (人群调整)",
                    publication_year=target.publication_year,
                    data_quality=target.data_quality,
                    weight=target.weight,
                    priority=target.priority
                )

                adjusted_targets[adjustment_key] = adjusted_target

        logger.info(f"完成人群特异性调整: {len(adjusted_targets)} 个目标")
        return adjusted_targets

    def model_temporal_trends(
        self,
        target_type: TargetType,
        historical_data: Dict[int, Dict[str, Dict[str, float]]]
    ) -> Dict[str, Any]:
        """
        建模目标值的时间趋势

        Args:
            target_type: 目标类型
            historical_data: 历史数据 {year: {age_group: {gender: value}}}

        Returns:
            时间趋势模型结果
        """
        trend_results = {
            'trends_by_group': {},
            'overall_trend': {},
            'projections': {}
        }

        # 按年龄组和性别分析趋势
        for age_group in set().union(*[data.keys() for data in historical_data.values()]):
            for gender in ['male', 'female']:
                # 收集时间序列数据
                time_series = []
                for year, year_data in historical_data.items():
                    if age_group in year_data and gender in year_data[age_group]:
                        time_series.append((year, year_data[age_group][gender]))

                if len(time_series) >= 3:  # 需要至少3个数据点
                    years, values = zip(*time_series)
                    years = np.array(years)
                    values = np.array(values)

                    # 线性趋势拟合
                    try:
                        slope, intercept = np.polyfit(years, values, 1)

                        # 计算拟合优度
                        fitted_values = slope * years + intercept
                        r_squared = 1 - np.sum((values - fitted_values) ** 2) / np.sum((values - np.mean(values)) ** 2)

                        group_key = f"{age_group}_{gender}"
                        trend_results['trends_by_group'][group_key] = {
                            'slope': slope,
                            'intercept': intercept,
                            'r_squared': r_squared,
                            'data_points': len(time_series),
                            'trend_direction': 'increasing' if slope > 0 else 'decreasing' if slope < 0 else 'stable'
                        }

                        # 未来5年预测
                        future_years = [max(years) + i for i in range(1, 6)]
                        projections = [slope * year + intercept for year in future_years]
                        trend_results['projections'][group_key] = dict(zip(future_years, projections))

                    except Exception as e:
                        logger.warning(f"趋势拟合失败 {group_key}: {e}")

        # 计算总体趋势
        all_slopes = [trend['slope'] for trend in trend_results['trends_by_group'].values()]
        if all_slopes:
            trend_results['overall_trend'] = {
                'mean_slope': np.mean(all_slopes),
                'median_slope': np.median(all_slopes),
                'slope_std': np.std(all_slopes),
                'groups_increasing': sum(1 for s in all_slopes if s > 0.001),
                'groups_decreasing': sum(1 for s in all_slopes if s < -0.001),
                'groups_stable': sum(1 for s in all_slopes if abs(s) <= 0.001)
            }

        logger.info(f"完成时间趋势建模: {len(trend_results['trends_by_group'])} 个组别")
        return trend_results
