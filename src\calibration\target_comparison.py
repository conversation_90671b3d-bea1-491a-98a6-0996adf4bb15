"""
校准目标与模型输出比较模块
实现多种比较指标、统计检验和自动评估功能
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from scipy import stats
import pandas as pd

from .targets import CalibrationTargets, CalibrationTarget, TargetType

logger = logging.getLogger(__name__)


@dataclass
class ComparisonMetrics:
    """比较指标数据类"""
    weighted_mse: float
    weighted_mae: float
    weighted_mape: float
    correlation: float
    chi2_statistic: float
    chi2_p_value: float
    r_squared: float
    fit_quality: str


@dataclass
class ComparisonResult:
    """比较结果数据类"""
    target_type: str
    metrics: ComparisonMetrics
    individual_comparisons: Dict[str, Dict[str, float]]
    statistical_tests: Dict[str, Any]
    recommendations: List[str]
    timestamp: datetime


class TargetComparator:
    """校准目标比较器"""
    
    def __init__(self, targets: CalibrationTargets):
        """
        初始化比较器
        
        Args:
            targets: 校准目标管理系统
        """
        self.targets = targets
        
        # 拟合质量阈值
        self.quality_thresholds = {
            'excellent': {'mape': 5, 'correlation': 0.9, 'chi2_p': 0.05},
            'good': {'mape': 10, 'correlation': 0.8, 'chi2_p': 0.01},
            'fair': {'mape': 20, 'correlation': 0.6, 'chi2_p': 0.001},
            'poor': {'mape': float('inf'), 'correlation': 0, 'chi2_p': 0}
        }
        
        logger.info("校准目标比较器初始化完成")
    
    def compare_outputs(
        self, 
        model_outputs: Dict[str, float],
        include_confidence_intervals: bool = True
    ) -> Dict[str, Any]:
        """
        比较模型输出与校准目标
        
        Args:
            model_outputs: 模型输出值字典
            include_confidence_intervals: 是否包含置信区间分析
            
        Returns:
            比较结果字典
        """
        comparison_results = {
            'overall_fit': {},
            'target_specific_fit': {},
            'statistical_tests': {},
            'recommendations': [],
            'timestamp': datetime.now().isoformat()
        }
        
        # 按目标类型分组比较
        for target_type in TargetType:
            targets_of_type = self.targets.get_targets_by_type(target_type)
            if not targets_of_type:
                continue
            
            type_result = self._compare_target_type(
                target_type, targets_of_type, model_outputs, include_confidence_intervals
            )
            comparison_results['target_specific_fit'][target_type.value] = type_result
        
        # 计算总体拟合度
        comparison_results['overall_fit'] = self._calculate_overall_fit(comparison_results)
        
        # 执行统计检验
        comparison_results['statistical_tests'] = self._perform_statistical_tests(
            model_outputs, comparison_results
        )
        
        # 生成改进建议
        comparison_results['recommendations'] = self._generate_recommendations(comparison_results)
        
        logger.info("模型输出比较完成")
        return comparison_results
    
    def _compare_target_type(
        self, 
        target_type: TargetType, 
        targets: List[CalibrationTarget],
        model_outputs: Dict[str, float],
        include_confidence_intervals: bool
    ) -> Dict[str, Any]:
        """比较特定类型的目标"""
        observed_values = []
        expected_values = []
        weights = []
        confidence_intervals = []
        target_keys = []
        
        # 收集数据
        for target in targets:
            target_key = f"{target_type.value}_{target.age_group}_{target.gender}"
            
            if target_key in model_outputs:
                observed_values.append(model_outputs[target_key])
                expected_values.append(target.value)
                weights.append(target.weight)
                target_keys.append(target_key)
                
                if include_confidence_intervals and target.confidence_interval:
                    confidence_intervals.append(target.confidence_interval)
                else:
                    confidence_intervals.append(None)
        
        if not observed_values:
            return {'error': 'No matching model outputs found'}
        
        observed = np.array(observed_values)
        expected = np.array(expected_values)
        weights = np.array(weights)
        
        # 计算比较指标
        metrics = self._calculate_comparison_metrics(observed, expected, weights)
        
        # 个体比较
        individual_comparisons = {}
        for i, key in enumerate(target_keys):
            individual_comparisons[key] = {
                'observed': observed[i],
                'expected': expected[i],
                'absolute_error': abs(observed[i] - expected[i]),
                'relative_error': abs(observed[i] - expected[i]) / (expected[i] + 1e-8),
                'weight': weights[i],
                'within_ci': self._check_within_confidence_interval(
                    observed[i], confidence_intervals[i]
                ) if confidence_intervals[i] else None
            }
        
        # 统计检验
        statistical_tests = self._perform_target_specific_tests(observed, expected, weights)
        
        return {
            'metrics': metrics.__dict__,
            'individual_comparisons': individual_comparisons,
            'statistical_tests': statistical_tests,
            'sample_size': len(observed)
        }
    
    def _calculate_comparison_metrics(
        self, 
        observed: np.ndarray, 
        expected: np.ndarray, 
        weights: np.ndarray
    ) -> ComparisonMetrics:
        """计算比较指标"""
        # 加权均方误差
        wmse = np.average((observed - expected) ** 2, weights=weights)
        
        # 加权平均绝对误差
        wmae = np.average(np.abs(observed - expected), weights=weights)
        
        # 加权平均绝对百分比误差
        wmape = np.average(
            np.abs((observed - expected) / (expected + 1e-8)), 
            weights=weights
        ) * 100
        
        # 相关系数
        if len(observed) > 1:
            try:
                correlation = np.corrcoef(observed, expected)[0, 1]
                # 处理NaN值
                if np.isnan(correlation):
                    correlation = 0.0
            except:
                correlation = 0.0
        else:
            correlation = 0.0
        
        # 卡方拟合优度检验
        chi2_stat, chi2_p = self._chi_square_goodness_of_fit(observed, expected, weights)
        
        # R²计算
        ss_res = np.sum(weights * (observed - expected) ** 2)
        ss_tot = np.sum(weights * (expected - np.average(expected, weights=weights)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
        
        # 拟合质量评级
        fit_quality = self._assess_fit_quality(wmape, correlation, chi2_p)
        
        return ComparisonMetrics(
            weighted_mse=wmse,
            weighted_mae=wmae,
            weighted_mape=wmape,
            correlation=correlation,
            chi2_statistic=chi2_stat,
            chi2_p_value=chi2_p,
            r_squared=r_squared,
            fit_quality=fit_quality
        )
    
    def _chi_square_goodness_of_fit(
        self, 
        observed: np.ndarray, 
        expected: np.ndarray, 
        weights: np.ndarray
    ) -> Tuple[float, float]:
        """卡方拟合优度检验"""
        # 避免除零
        expected_safe = np.where(expected == 0, 1e-8, expected)
        
        # 加权卡方统计量
        chi2_contributions = weights * ((observed - expected_safe) ** 2) / expected_safe
        chi2_stat = np.sum(chi2_contributions)
        
        # 自由度
        df = max(1, len(observed) - 1)
        
        # p值计算
        try:
            p_value = 1 - stats.chi2.cdf(chi2_stat, df)
        except:
            p_value = 0.0
        
        return chi2_stat, p_value
    
    def _assess_fit_quality(
        self, 
        wmape: float, 
        correlation: float, 
        chi2_p: float
    ) -> str:
        """评估拟合质量"""
        # 综合评分
        score = 0
        
        # MAPE评分
        if wmape < self.quality_thresholds['excellent']['mape']:
            score += 3
        elif wmape < self.quality_thresholds['good']['mape']:
            score += 2
        elif wmape < self.quality_thresholds['fair']['mape']:
            score += 1
        
        # 相关性评分
        if correlation > self.quality_thresholds['excellent']['correlation']:
            score += 3
        elif correlation > self.quality_thresholds['good']['correlation']:
            score += 2
        elif correlation > self.quality_thresholds['fair']['correlation']:
            score += 1
        
        # 卡方检验评分
        if chi2_p > self.quality_thresholds['excellent']['chi2_p']:
            score += 2
        elif chi2_p > self.quality_thresholds['good']['chi2_p']:
            score += 1
        
        # 质量等级
        if score >= 7:
            return "优秀"
        elif score >= 5:
            return "良好"
        elif score >= 3:
            return "中等"
        else:
            return "较差"
    
    def _check_within_confidence_interval(
        self, 
        observed_value: float, 
        confidence_interval: Optional[Tuple[float, float]]
    ) -> Optional[bool]:
        """检查观测值是否在置信区间内"""
        if confidence_interval is None:
            return None
        
        lower, upper = confidence_interval
        return lower <= observed_value <= upper
    
    def _perform_target_specific_tests(
        self, 
        observed: np.ndarray, 
        expected: np.ndarray, 
        weights: np.ndarray
    ) -> Dict[str, Any]:
        """执行目标特异性统计检验"""
        tests = {}
        
        if len(observed) > 2:
            # Kolmogorov-Smirnov检验
            try:
                ks_stat, ks_p = stats.kstest(observed, expected)
                tests['kolmogorov_smirnov'] = {
                    'statistic': ks_stat,
                    'p_value': ks_p,
                    'interpretation': 'distributions_similar' if ks_p > 0.05 else 'distributions_different'
                }
            except:
                pass
            
            # Shapiro-Wilk正态性检验（残差）
            residuals = observed - expected
            if len(residuals) >= 3:
                try:
                    sw_stat, sw_p = stats.shapiro(residuals)
                    tests['shapiro_wilk'] = {
                        'statistic': sw_stat,
                        'p_value': sw_p,
                        'interpretation': 'residuals_normal' if sw_p > 0.05 else 'residuals_non_normal'
                    }
                except:
                    pass
        
        return tests
    
    def _calculate_overall_fit(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算总体拟合度"""
        target_fits = comparison_results.get('target_specific_fit', {})
        
        if not target_fits:
            return {'error': 'No target-specific fits available'}
        
        # 收集所有指标
        all_mapes = []
        all_correlations = []
        all_r_squareds = []
        quality_counts = {'优秀': 0, '良好': 0, '中等': 0, '较差': 0}
        
        for target_type, fit_data in target_fits.items():
            if 'error' in fit_data:
                continue
            
            metrics = fit_data.get('metrics', {})
            all_mapes.append(metrics.get('weighted_mape', 0))
            all_correlations.append(metrics.get('correlation', 0))
            all_r_squareds.append(metrics.get('r_squared', 0))
            
            quality = metrics.get('fit_quality', '较差')
            quality_counts[quality] = quality_counts.get(quality, 0) + 1
        
        # 计算总体指标
        overall_fit = {
            'mean_mape': np.mean(all_mapes) if all_mapes else 0,
            'mean_correlation': np.mean(all_correlations) if all_correlations else 0,
            'mean_r_squared': np.mean(all_r_squareds) if all_r_squareds else 0,
            'quality_distribution': quality_counts,
            'total_target_types': len(target_fits)
        }
        
        # 总体质量评级
        mean_mape = overall_fit['mean_mape']
        mean_correlation = overall_fit['mean_correlation']
        
        if mean_mape < 10 and mean_correlation > 0.8:
            overall_quality = "优秀"
        elif mean_mape < 20 and mean_correlation > 0.6:
            overall_quality = "良好"
        elif mean_mape < 30 and mean_correlation > 0.4:
            overall_quality = "中等"
        else:
            overall_quality = "较差"
        
        overall_fit['overall_quality'] = overall_quality
        
        return overall_fit
    
    def _perform_statistical_tests(
        self, 
        model_outputs: Dict[str, float],
        comparison_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行统计检验"""
        tests = {}
        
        # 收集所有观测值和期望值
        all_observed = []
        all_expected = []
        
        for target_type_data in comparison_results['target_specific_fit'].values():
            if 'error' in target_type_data:
                continue
            
            individual_comparisons = target_type_data.get('individual_comparisons', {})
            for comparison in individual_comparisons.values():
                all_observed.append(comparison['observed'])
                all_expected.append(comparison['expected'])
        
        if len(all_observed) > 2:
            all_observed = np.array(all_observed)
            all_expected = np.array(all_expected)
            
            # 配对t检验
            try:
                t_stat, t_p = stats.ttest_rel(all_observed, all_expected)
                tests['paired_t_test'] = {
                    'statistic': t_stat,
                    'p_value': t_p,
                    'interpretation': 'no_significant_difference' if t_p > 0.05 else 'significant_difference'
                }
            except:
                pass
            
            # Wilcoxon符号秩检验
            try:
                w_stat, w_p = stats.wilcoxon(all_observed, all_expected)
                tests['wilcoxon_signed_rank'] = {
                    'statistic': w_stat,
                    'p_value': w_p,
                    'interpretation': 'no_significant_difference' if w_p > 0.05 else 'significant_difference'
                }
            except:
                pass
        
        return tests
    
    def _generate_recommendations(self, comparison_results: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        overall_fit = comparison_results.get('overall_fit', {})
        overall_quality = overall_fit.get('overall_quality', '较差')
        
        if overall_quality == '较差':
            recommendations.append("模型拟合质量较差，建议重新校准参数")
            recommendations.append("检查模型结构是否合适，考虑增加复杂度")
        elif overall_quality == '中等':
            recommendations.append("模型拟合质量中等，可以进一步优化")
            recommendations.append("关注拟合较差的特定目标类型")
        
        # 基于MAPE的建议
        mean_mape = overall_fit.get('mean_mape', 0)
        if mean_mape > 20:
            recommendations.append(f"平均绝对百分比误差较高({mean_mape:.1f}%)，需要改进模型精度")
        
        # 基于相关性的建议
        mean_correlation = overall_fit.get('mean_correlation', 0)
        if mean_correlation < 0.6:
            recommendations.append(f"模型输出与目标相关性较低({mean_correlation:.2f})，检查模型逻辑")
        
        # 基于质量分布的建议
        quality_dist = overall_fit.get('quality_distribution', {})
        poor_count = quality_dist.get('较差', 0)
        total_types = overall_fit.get('total_target_types', 1)
        
        if poor_count / total_types > 0.5:
            recommendations.append("超过一半的目标类型拟合较差，建议全面检查模型")
        
        if not recommendations:
            recommendations.append("模型拟合质量良好，建议继续监控和验证")
        
        return recommendations
