#!/usr/bin/env python3
"""
校准目标管理系统演示脚本
展示Story 5.3实现的主要功能
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from calibration.targets import CalibrationTargets, CalibrationTarget, TargetType, DataQuality
from calibration.target_weights import TargetWeightManager
from calibration.target_comparison import TargetComparator
from calibration.data_validator import DataValidator


def create_sample_data():
    """创建示例校准目标数据"""
    print("🎯 创建示例校准目标数据...")
    
    targets = [
        # 腺瘤患病率数据
        CalibrationTarget(
            target_type=TargetType.ADENOMA_PREVALENCE,
            age_group="50-54",
            gender="male",
            value=0.15,
            standard_error=0.02,
            confidence_interval=(0.11, 0.19),
            sample_size=1000,
            data_source="SEER Database 2023",
            publication_year=2023,
            data_quality=DataQuality.HIGH,
            weight=1.0,
            priority=1
        ),
        CalibrationTarget(
            target_type=TargetType.ADENOMA_PREVALENCE,
            age_group="50-54",
            gender="female",
            value=0.12,
            standard_error=0.018,
            confidence_interval=(0.084, 0.156),
            sample_size=1200,
            data_source="SEER Database 2023",
            publication_year=2023,
            data_quality=DataQuality.HIGH,
            weight=1.0,
            priority=1
        ),
        CalibrationTarget(
            target_type=TargetType.ADENOMA_PREVALENCE,
            age_group="60-64",
            gender="male",
            value=0.25,
            standard_error=0.03,
            confidence_interval=(0.19, 0.31),
            sample_size=800,
            data_source="SEER Database 2023",
            publication_year=2023,
            data_quality=DataQuality.HIGH,
            weight=1.0,
            priority=1
        ),
        
        # 癌症发病率数据
        CalibrationTarget(
            target_type=TargetType.CANCER_INCIDENCE,
            age_group="60-64",
            gender="male",
            value=0.008,
            standard_error=0.001,
            confidence_interval=(0.006, 0.010),
            sample_size=5000,
            data_source="National Cancer Registry",
            publication_year=2022,
            data_quality=DataQuality.MEDIUM,
            weight=2.0,
            priority=2
        ),
        CalibrationTarget(
            target_type=TargetType.CANCER_INCIDENCE,
            age_group="70-74",
            gender="male",
            value=0.015,
            standard_error=0.002,
            confidence_interval=(0.011, 0.019),
            sample_size=3000,
            data_source="National Cancer Registry",
            publication_year=2022,
            data_quality=DataQuality.MEDIUM,
            weight=2.0,
            priority=2
        ),
        
        # 癌症死亡率数据
        CalibrationTarget(
            target_type=TargetType.CANCER_MORTALITY,
            age_group="70-74",
            gender="male",
            value=0.003,
            standard_error=0.0005,
            sample_size=2000,
            data_source="Vital Statistics",
            publication_year=2021,
            data_quality=DataQuality.LOW,
            weight=3.0,
            priority=3
        )
    ]
    
    print(f"✅ 创建了 {len(targets)} 个校准目标")
    return targets


def demonstrate_targets_management():
    """演示校准目标管理功能"""
    print("\n" + "="*60)
    print("📊 校准目标管理系统演示")
    print("="*60)
    
    # 1. 创建目标管理器
    print("\n1️⃣ 初始化校准目标管理器")
    targets_manager = CalibrationTargets(storage_path="demo_data/calibration_targets")
    
    # 2. 添加示例数据
    sample_targets = create_sample_data()
    for target in sample_targets:
        targets_manager.add_target(target)
    
    # 3. 显示统计信息
    print("\n2️⃣ 数据统计信息")
    stats = targets_manager.get_summary_statistics()
    print(f"   总目标数: {stats['total_targets']}")
    print(f"   目标类型分布:")
    for target_type, count in stats['target_types'].items():
        if count > 0:
            print(f"     - {target_type}: {count}")
    print(f"   年龄组: {', '.join(stats['age_groups'])}")
    print(f"   性别: {', '.join(stats['genders'])}")
    
    # 4. 演示年龄插值功能
    print("\n3️⃣ 年龄特异性插值")
    target_ages = [52, 57, 62]
    interpolated = targets_manager.interpolate_age_specific_targets(
        TargetType.ADENOMA_PREVALENCE, "male", target_ages
    )
    print(f"   男性腺瘤患病率插值结果:")
    for age, value in interpolated.items():
        print(f"     - {age}岁: {value:.4f}")
    
    # 5. 演示性别差异建模
    print("\n4️⃣ 性别差异建模")
    gender_results = targets_manager.model_gender_specific_differences(
        TargetType.ADENOMA_PREVALENCE, ["50-54", "60-64"]
    )
    print(f"   性别比率 (男性/女性):")
    for age_group, ratio in gender_results['gender_ratios'].items():
        print(f"     - {age_group}: {ratio:.2f}")
    
    return targets_manager


def demonstrate_weight_management(targets_manager):
    """演示权重管理功能"""
    print("\n" + "="*60)
    print("⚖️ 权重管理系统演示")
    print("="*60)
    
    # 1. 创建权重管理器
    print("\n1️⃣ 初始化权重管理器")
    weight_manager = TargetWeightManager(storage_path="demo_data/calibration_weights")
    
    all_targets = targets_manager.get_all_targets()
    
    # 2. 自动权重计算
    print("\n2️⃣ 自动权重计算")
    auto_weights = weight_manager.calculate_automatic_weights(all_targets)
    print("   基于数据质量的自动权重:")
    for key, weight in list(auto_weights.items())[:3]:  # 显示前3个
        print(f"     - {key}: {weight:.3f}")
    
    # 3. 不同优化方法
    print("\n3️⃣ 权重优化方法对比")
    methods = ["inverse_variance", "equal"]
    
    for method in methods:
        weights = weight_manager.optimize_weights(all_targets, optimization_method=method)
        avg_weight = np.mean(list(weights.values()))
        print(f"   {method} 方法平均权重: {avg_weight:.3f}")
    
    # 4. 敏感性分析
    print("\n4️⃣ 权重敏感性分析")
    sensitivity_results = weight_manager.perform_sensitivity_analysis(all_targets)
    print("   敏感性分数 (前3个):")
    sensitivity_scores = sensitivity_results['sensitivity_scores']
    for key, score in list(sensitivity_scores.items())[:3]:
        print(f"     - {key}: {score:.3f}")
    
    return weight_manager


def demonstrate_data_validation(targets_manager):
    """演示数据验证功能"""
    print("\n" + "="*60)
    print("🔍 数据质量验证演示")
    print("="*60)
    
    # 1. 创建验证器
    print("\n1️⃣ 执行数据质量验证")
    validator = DataValidator()
    all_targets = targets_manager.get_all_targets()
    
    # 2. 验证数据
    quality_report = validator.validate_targets(all_targets)
    
    print(f"   总体质量分数: {quality_report.overall_score:.2f}")
    print(f"   验证结果统计:")
    
    error_count = sum(1 for r in quality_report.validation_results if r.status == "error")
    warning_count = sum(1 for r in quality_report.validation_results if r.status == "warning")
    
    print(f"     - 错误: {error_count}")
    print(f"     - 警告: {warning_count}")
    
    # 3. 显示改进建议
    print("\n2️⃣ 改进建议:")
    for i, recommendation in enumerate(quality_report.recommendations[:3], 1):
        print(f"   {i}. {recommendation}")
    
    return quality_report


def demonstrate_comparison_analysis(targets_manager):
    """演示比较分析功能"""
    print("\n" + "="*60)
    print("📈 模型输出比较分析演示")
    print("="*60)
    
    # 1. 创建比较器
    print("\n1️⃣ 初始化比较分析器")
    comparator = TargetComparator(targets_manager)
    
    # 2. 模拟模型输出
    print("\n2️⃣ 模拟模型输出数据")
    model_outputs = {
        "adenoma_prevalence_50-54_male": 0.14,      # 略低于目标0.15
        "adenoma_prevalence_50-54_female": 0.13,    # 略高于目标0.12
        "adenoma_prevalence_60-64_male": 0.24,      # 略低于目标0.25
        "cancer_incidence_60-64_male": 0.009,       # 略高于目标0.008
        "cancer_incidence_70-74_male": 0.016,       # 略高于目标0.015
        "cancer_mortality_70-74_male": 0.0028       # 略低于目标0.003
    }
    
    print("   模型输出示例:")
    for key, value in list(model_outputs.items())[:3]:
        print(f"     - {key}: {value:.4f}")
    
    # 3. 执行比较分析
    print("\n3️⃣ 执行比较分析")
    comparison_results = comparator.compare_outputs(model_outputs)
    
    # 4. 显示结果
    overall_fit = comparison_results['overall_fit']
    print(f"   总体拟合质量: {overall_fit['overall_quality']}")
    print(f"   平均绝对百分比误差: {overall_fit['mean_mape']:.2f}%")
    print(f"   平均相关系数: {overall_fit['mean_correlation']:.3f}")
    
    # 5. 按目标类型显示结果
    print("\n4️⃣ 分类型拟合结果:")
    target_fits = comparison_results['target_specific_fit']
    for target_type, fit_data in target_fits.items():
        if 'error' not in fit_data:
            metrics = fit_data['metrics']
            print(f"   {target_type}:")
            print(f"     - 拟合质量: {metrics['fit_quality']}")
            print(f"     - MAPE: {metrics['weighted_mape']:.2f}%")
    
    # 6. 显示改进建议
    print("\n5️⃣ 改进建议:")
    recommendations = comparison_results['recommendations']
    for i, rec in enumerate(recommendations[:3], 1):
        print(f"   {i}. {rec}")
    
    return comparison_results


def demonstrate_version_control(targets_manager):
    """演示版本控制功能"""
    print("\n" + "="*60)
    print("🗂️ 版本控制演示")
    print("="*60)
    
    # 1. 创建初始版本
    print("\n1️⃣ 创建版本快照")
    v1_id = targets_manager.create_version_snapshot("初始演示数据")
    print(f"   创建版本: {v1_id}")
    
    # 2. 添加新目标
    print("\n2️⃣ 添加新目标并创建新版本")
    new_target = CalibrationTarget(
        target_type=TargetType.SURVIVAL_RATE,
        age_group="65-69",
        gender="female",
        value=0.85,
        standard_error=0.05,
        data_quality=DataQuality.MEDIUM
    )
    targets_manager.add_target(new_target)
    
    v2_id = targets_manager.create_version_snapshot("添加生存率数据")
    print(f"   创建版本: {v2_id}")
    print(f"   当前目标数: {targets_manager.metadata.total_targets}")
    
    # 3. 创建备份
    print("\n3️⃣ 创建数据备份")
    backup_path = targets_manager.create_backup("演示备份")
    print(f"   备份路径: {backup_path}")
    
    # 4. 版本历史
    print("\n4️⃣ 版本历史:")
    for version in targets_manager.version_history:
        print(f"   - {version['version_id']}: {version['description']}")
    
    return v1_id, v2_id


def main():
    """主演示函数"""
    print("🚀 校准目标管理系统 (Story 5.3) 功能演示")
    print("=" * 80)
    
    try:
        # 创建演示数据目录
        os.makedirs("demo_data", exist_ok=True)
        
        # 1. 目标管理演示
        targets_manager = demonstrate_targets_management()
        
        # 2. 权重管理演示
        weight_manager = demonstrate_weight_management(targets_manager)
        
        # 3. 数据验证演示
        quality_report = demonstrate_data_validation(targets_manager)
        
        # 4. 比较分析演示
        comparison_results = demonstrate_comparison_analysis(targets_manager)
        
        # 5. 版本控制演示
        v1_id, v2_id = demonstrate_version_control(targets_manager)
        
        # 6. 保存演示结果
        print("\n" + "="*60)
        print("💾 保存演示结果")
        print("="*60)
        
        targets_manager.save_to_file("demo_data/demo_targets.json")
        weight_manager.save_schemes("demo_data/demo_weights.json")
        
        print("   ✅ 校准目标已保存到: demo_data/demo_targets.json")
        print("   ✅ 权重方案已保存到: demo_data/demo_weights.json")
        
        print("\n🎉 演示完成！")
        print("\n📋 功能总结:")
        print("   ✅ 校准目标数据导入和管理")
        print("   ✅ 年龄性别特异性配置和插值")
        print("   ✅ 权重和优先级设置系统")
        print("   ✅ 数据质量检查和验证")
        print("   ✅ 模型输出比较分析")
        print("   ✅ 可视化展示功能框架")
        print("   ✅ 版本控制和备份恢复")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
