"""
校准目标比较器单元测试
"""

import pytest
import numpy as np
from unittest.mock import Mock

from src.calibration.targets import CalibrationTargets, CalibrationTarget, TargetType, DataQuality
from src.calibration.target_comparison import TargetComparator, ComparisonMetrics


class TestTargetComparator:
    """测试TargetComparator类"""
    
    @pytest.fixture
    def targets_manager(self):
        """创建目标管理器"""
        manager = CalibrationTargets()
        
        # 添加测试数据
        targets = [
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="male",
                value=0.15,
                standard_error=0.02,
                confidence_interval=(0.11, 0.19),
                weight=1.0
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="female",
                value=0.12,
                standard_error=0.018,
                confidence_interval=(0.084, 0.156),
                weight=1.0
            ),
            CalibrationTarget(
                target_type=TargetType.CANCER_INCIDENCE,
                age_group="60-64",
                gender="male",
                value=0.008,
                standard_error=0.001,
                confidence_interval=(0.006, 0.010),
                weight=2.0
            ),
            CalibrationTarget(
                target_type=TargetType.CANCER_INCIDENCE,
                age_group="60-64",
                gender="female",
                value=0.006,
                standard_error=0.0008,
                confidence_interval=(0.0044, 0.0076),
                weight=2.0
            )
        ]
        
        for target in targets:
            manager.add_target(target)
        
        return manager
    
    @pytest.fixture
    def comparator(self, targets_manager):
        """创建比较器"""
        return TargetComparator(targets_manager)
    
    @pytest.fixture
    def perfect_model_outputs(self):
        """完美匹配的模型输出"""
        return {
            "adenoma_prevalence_50-54_male": 0.15,
            "adenoma_prevalence_50-54_female": 0.12,
            "cancer_incidence_60-64_male": 0.008,
            "cancer_incidence_60-64_female": 0.006
        }
    
    @pytest.fixture
    def imperfect_model_outputs(self):
        """有误差的模型输出"""
        return {
            "adenoma_prevalence_50-54_male": 0.13,    # 误差: -0.02
            "adenoma_prevalence_50-54_female": 0.14,  # 误差: +0.02
            "cancer_incidence_60-64_male": 0.010,     # 误差: +0.002
            "cancer_incidence_60-64_female": 0.005    # 误差: -0.001
        }
    
    def test_comparator_initialization(self, comparator, targets_manager):
        """测试比较器初始化"""
        assert comparator.targets == targets_manager
        assert 'excellent' in comparator.quality_thresholds
        assert 'good' in comparator.quality_thresholds
        assert 'fair' in comparator.quality_thresholds
        assert 'poor' in comparator.quality_thresholds
    
    def test_perfect_comparison(self, comparator, perfect_model_outputs):
        """测试完美匹配的比较"""
        results = comparator.compare_outputs(perfect_model_outputs)
        
        # 检查结果结构
        assert 'overall_fit' in results
        assert 'target_specific_fit' in results
        assert 'statistical_tests' in results
        assert 'recommendations' in results
        assert 'timestamp' in results
        
        # 检查总体拟合
        overall_fit = results['overall_fit']
        assert overall_fit['overall_quality'] == "优秀"
        assert overall_fit['mean_mape'] < 1.0  # 完美匹配应该MAPE很小
        assert overall_fit['mean_correlation'] > 0.99  # 相关性应该接近1
    
    def test_imperfect_comparison(self, comparator, imperfect_model_outputs):
        """测试有误差的比较"""
        results = comparator.compare_outputs(imperfect_model_outputs)
        
        # 检查结果结构
        assert 'overall_fit' in results
        assert 'target_specific_fit' in results
        
        # 总体拟合应该不是优秀
        overall_fit = results['overall_fit']
        assert overall_fit['overall_quality'] in ["良好", "中等", "较差"]
        assert overall_fit['mean_mape'] > 0  # 应该有误差
        
        # 检查目标特异性拟合
        target_fits = results['target_specific_fit']
        assert 'adenoma_prevalence' in target_fits
        assert 'cancer_incidence' in target_fits
        
        # 检查腺瘤患病率拟合
        adenoma_fit = target_fits['adenoma_prevalence']
        assert 'metrics' in adenoma_fit
        assert 'individual_comparisons' in adenoma_fit
        
        metrics = adenoma_fit['metrics']
        assert metrics['weighted_mape'] > 0
        # 相关系数可能为NaN或在[-1,1]范围内
        correlation = metrics['correlation']
        assert np.isnan(correlation) or (-1 <= correlation <= 1)

        # R²可能为负值（拟合很差时）
        r_squared = metrics['r_squared']
        assert r_squared <= 1
        # 对于极差的拟合，R²可能为负，这是正常的
    
    def test_comparison_metrics_calculation(self, comparator):
        """测试比较指标计算"""
        observed = np.array([0.13, 0.14, 0.010, 0.005])
        expected = np.array([0.15, 0.12, 0.008, 0.006])
        weights = np.array([1.0, 1.0, 2.0, 2.0])
        
        metrics = comparator._calculate_comparison_metrics(observed, expected, weights)
        
        assert isinstance(metrics, ComparisonMetrics)
        assert metrics.weighted_mse > 0
        assert metrics.weighted_mae > 0
        assert metrics.weighted_mape > 0
        assert -1 <= metrics.correlation <= 1
        assert metrics.chi2_statistic >= 0
        assert 0 <= metrics.chi2_p_value <= 1
        assert metrics.fit_quality in ["优秀", "良好", "中等", "较差"]
    
    def test_chi_square_goodness_of_fit(self, comparator):
        """测试卡方拟合优度检验"""
        observed = np.array([0.13, 0.14, 0.010])
        expected = np.array([0.15, 0.12, 0.008])
        weights = np.array([1.0, 1.0, 2.0])
        
        chi2_stat, p_value = comparator._chi_square_goodness_of_fit(observed, expected, weights)
        
        assert chi2_stat >= 0
        assert 0 <= p_value <= 1
    
    def test_fit_quality_assessment(self, comparator):
        """测试拟合质量评估"""
        # 优秀拟合
        quality = comparator._assess_fit_quality(wmape=3.0, correlation=0.95, chi2_p=0.1)
        assert quality == "优秀"
        
        # 良好拟合
        quality = comparator._assess_fit_quality(wmape=8.0, correlation=0.85, chi2_p=0.03)
        assert quality == "良好"
        
        # 中等拟合
        quality = comparator._assess_fit_quality(wmape=15.0, correlation=0.7, chi2_p=0.02)
        assert quality == "中等"
        
        # 较差拟合
        quality = comparator._assess_fit_quality(wmape=30.0, correlation=0.3, chi2_p=0.0001)
        assert quality == "较差"
    
    def test_confidence_interval_check(self, comparator):
        """测试置信区间检查"""
        # 在置信区间内
        result = comparator._check_within_confidence_interval(0.14, (0.11, 0.19))
        assert result is True
        
        # 在置信区间外
        result = comparator._check_within_confidence_interval(0.10, (0.11, 0.19))
        assert result is False
        
        # 没有置信区间
        result = comparator._check_within_confidence_interval(0.14, None)
        assert result is None
    
    def test_individual_comparisons(self, comparator, imperfect_model_outputs):
        """测试个体比较"""
        results = comparator.compare_outputs(imperfect_model_outputs)
        
        # 检查腺瘤患病率的个体比较
        adenoma_fit = results['target_specific_fit']['adenoma_prevalence']
        individual_comparisons = adenoma_fit['individual_comparisons']
        
        # 检查男性50-54岁组
        male_key = "adenoma_prevalence_50-54_male"
        assert male_key in individual_comparisons
        
        male_comparison = individual_comparisons[male_key]
        assert male_comparison['observed'] == 0.13
        assert male_comparison['expected'] == 0.15
        assert abs(male_comparison['absolute_error'] - 0.02) < 0.001
        assert abs(male_comparison['relative_error'] - 0.02/0.15) < 0.001
        assert float(male_comparison['weight']) == 1.0
        assert male_comparison['within_ci'] == True  # 0.13在(0.11, 0.19)内
    
    def test_statistical_tests(self, comparator, imperfect_model_outputs):
        """测试统计检验"""
        results = comparator.compare_outputs(imperfect_model_outputs)
        
        statistical_tests = results['statistical_tests']
        
        # 应该包含配对t检验
        if 'paired_t_test' in statistical_tests:
            t_test = statistical_tests['paired_t_test']
            assert 'statistic' in t_test
            assert 'p_value' in t_test
            assert 'interpretation' in t_test
            assert t_test['interpretation'] in ['no_significant_difference', 'significant_difference']
        
        # 应该包含Wilcoxon符号秩检验
        if 'wilcoxon_signed_rank' in statistical_tests:
            w_test = statistical_tests['wilcoxon_signed_rank']
            assert 'statistic' in w_test
            assert 'p_value' in w_test
            assert 'interpretation' in w_test
    
    def test_target_specific_statistical_tests(self, comparator):
        """测试目标特异性统计检验"""
        observed = np.array([0.13, 0.14, 0.010])
        expected = np.array([0.15, 0.12, 0.008])
        weights = np.array([1.0, 1.0, 2.0])
        
        tests = comparator._perform_target_specific_tests(observed, expected, weights)
        
        # 检查是否包含预期的检验
        if 'kolmogorov_smirnov' in tests:
            ks_test = tests['kolmogorov_smirnov']
            assert 'statistic' in ks_test
            assert 'p_value' in ks_test
            assert 'interpretation' in ks_test
        
        if 'shapiro_wilk' in tests:
            sw_test = tests['shapiro_wilk']
            assert 'statistic' in sw_test
            assert 'p_value' in sw_test
            assert 'interpretation' in sw_test
    
    def test_recommendations_generation(self, comparator, imperfect_model_outputs):
        """测试改进建议生成"""
        results = comparator.compare_outputs(imperfect_model_outputs)
        
        recommendations = results['recommendations']
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # 建议应该是字符串
        for rec in recommendations:
            assert isinstance(rec, str)
            assert len(rec) > 0
    
    def test_empty_model_outputs(self, comparator):
        """测试空模型输出"""
        results = comparator.compare_outputs({})
        
        # 应该没有目标特异性拟合结果
        target_fits = results['target_specific_fit']
        for target_type, fit_data in target_fits.items():
            assert 'error' in fit_data
            assert fit_data['error'] == 'No matching model outputs found'
    
    def test_partial_model_outputs(self, comparator):
        """测试部分模型输出"""
        partial_outputs = {
            "adenoma_prevalence_50-54_male": 0.13,
            # 缺少其他目标的输出
        }
        
        results = comparator.compare_outputs(partial_outputs)
        
        # 腺瘤患病率应该有结果
        adenoma_fit = results['target_specific_fit']['adenoma_prevalence']
        assert 'error' not in adenoma_fit
        assert 'metrics' in adenoma_fit
        
        # 癌症发病率应该没有匹配的输出
        cancer_fit = results['target_specific_fit']['cancer_incidence']
        assert 'error' in cancer_fit
    
    def test_overall_fit_calculation(self, comparator, imperfect_model_outputs):
        """测试总体拟合度计算"""
        results = comparator.compare_outputs(imperfect_model_outputs)
        
        overall_fit = results['overall_fit']
        
        # 检查必需的字段
        assert 'mean_mape' in overall_fit
        assert 'mean_correlation' in overall_fit
        assert 'mean_r_squared' in overall_fit
        assert 'quality_distribution' in overall_fit
        assert 'total_target_types' in overall_fit
        assert 'overall_quality' in overall_fit
        
        # 检查数值合理性
        assert overall_fit['mean_mape'] >= 0
        # 相关系数可能为NaN，需要特殊处理
        mean_correlation = overall_fit['mean_correlation']
        assert np.isnan(mean_correlation) or (-1 <= mean_correlation <= 1)
        # R²可能为负值（拟合很差时）
        assert overall_fit['mean_r_squared'] <= 1
        assert overall_fit['total_target_types'] > 0
        assert overall_fit['overall_quality'] in ["优秀", "良好", "中等", "较差"]
        
        # 检查质量分布
        quality_dist = overall_fit['quality_distribution']
        assert isinstance(quality_dist, dict)
        total_quality_count = sum(quality_dist.values())
        assert total_quality_count == overall_fit['total_target_types']
    
    def test_comparison_with_confidence_intervals(self, comparator, imperfect_model_outputs):
        """测试包含置信区间的比较"""
        results = comparator.compare_outputs(imperfect_model_outputs, include_confidence_intervals=True)
        
        # 检查个体比较中是否包含置信区间信息
        adenoma_fit = results['target_specific_fit']['adenoma_prevalence']
        individual_comparisons = adenoma_fit['individual_comparisons']
        
        for comparison in individual_comparisons.values():
            assert 'within_ci' in comparison
            # within_ci应该是布尔值或None
            assert comparison['within_ci'] in [True, False, None]
    
    def test_weighted_metrics_calculation(self, comparator):
        """测试加权指标计算"""
        # 创建不同权重的数据
        observed = np.array([0.10, 0.20])
        expected = np.array([0.15, 0.15])
        weights = np.array([1.0, 3.0])  # 第二个目标权重更高
        
        metrics = comparator._calculate_comparison_metrics(observed, expected, weights)
        
        # 加权指标应该更偏向权重高的目标
        # 第二个目标误差更大但权重更高，所以加权误差应该较大
        assert metrics.weighted_mse > 0
        assert metrics.weighted_mae > 0
        assert metrics.weighted_mape > 0
