"""
校准目标管理系统集成测试
"""

import pytest
import tempfile
import json
import pandas as pd
from pathlib import Path

from src.calibration.targets import CalibrationTargets, CalibrationTarget, TargetType, DataQuality
from src.calibration.target_weights import TargetWeightManager
from src.calibration.target_comparison import TargetComparator
from src.calibration.data_validator import DataValidator


class TestTargetsManagementIntegration:
    """测试校准目标管理系统集成功能"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def sample_csv_data(self, temp_dir):
        """创建样本CSV数据文件"""
        data = [
            {
                'target_type': 'adenoma_prevalence',
                'age_group': '50-54',
                'gender': 'male',
                'value': 0.15,
                'standard_error': 0.02,
                'confidence_interval': '(0.11, 0.19)',
                'sample_size': 1000,
                'data_source': 'Study A',
                'publication_year': 2023,
                'data_quality': 'high',
                'weight': 1.0,
                'priority': 1
            },
            {
                'target_type': 'adenoma_prevalence',
                'age_group': '50-54',
                'gender': 'female',
                'value': 0.12,
                'standard_error': 0.018,
                'confidence_interval': '(0.084, 0.156)',
                'sample_size': 1200,
                'data_source': 'Study A',
                'publication_year': 2023,
                'data_quality': 'high',
                'weight': 1.0,
                'priority': 1
            },
            {
                'target_type': 'cancer_incidence',
                'age_group': '60-64',
                'gender': 'male',
                'value': 0.008,
                'standard_error': 0.001,
                'confidence_interval': '(0.006, 0.010)',
                'sample_size': 800,
                'data_source': 'Study B',
                'publication_year': 2022,
                'data_quality': 'medium',
                'weight': 2.0,
                'priority': 2
            },
            {
                'target_type': 'cancer_mortality',
                'age_group': '70-74',
                'gender': 'male',
                'value': 0.003,
                'standard_error': 0.0005,
                'sample_size': 500,
                'data_source': 'Study C',
                'publication_year': 2021,
                'data_quality': 'low',
                'weight': 3.0,
                'priority': 3
            }
        ]
        
        df = pd.DataFrame(data)
        csv_file = Path(temp_dir) / "sample_targets.csv"
        df.to_csv(csv_file, index=False)
        
        return csv_file
    
    def test_complete_workflow(self, temp_dir, sample_csv_data):
        """测试完整的工作流程"""
        # 1. 创建目标管理器并加载数据
        targets_manager = CalibrationTargets(storage_path=temp_dir)
        count = targets_manager.load_targets_from_file(sample_csv_data)
        
        assert count == 4
        assert targets_manager.metadata.total_targets == 4
        
        # 2. 验证数据质量
        validator = DataValidator()
        all_targets = targets_manager.get_all_targets()
        quality_report = validator.validate_targets(all_targets)
        
        assert quality_report.total_targets == 4
        assert quality_report.overall_score > 0
        assert len(quality_report.recommendations) > 0
        
        # 3. 创建权重管理器并计算权重
        weight_manager = TargetWeightManager(storage_path=temp_dir)
        automatic_weights = weight_manager.calculate_automatic_weights(all_targets)
        
        assert len(automatic_weights) == 4
        for weight in automatic_weights.values():
            assert weight > 0
        
        # 4. 创建比较器并进行比较分析
        comparator = TargetComparator(targets_manager)
        
        # 模拟模型输出
        model_outputs = {
            "adenoma_prevalence_50-54_male": 0.14,
            "adenoma_prevalence_50-54_female": 0.13,
            "cancer_incidence_60-64_male": 0.009,
            "cancer_mortality_70-74_male": 0.0025
        }
        
        comparison_results = comparator.compare_outputs(model_outputs)
        
        assert 'overall_fit' in comparison_results
        assert 'target_specific_fit' in comparison_results
        assert 'recommendations' in comparison_results
        
        # 5. 保存所有结果
        targets_manager.save_to_file(Path(temp_dir) / "targets.json")
        weight_manager.save_schemes(Path(temp_dir) / "weights.json")
        
        # 验证文件存在
        assert (Path(temp_dir) / "targets.json").exists()
        assert (Path(temp_dir) / "weights.json").exists()
    
    def test_data_import_and_validation_workflow(self, temp_dir, sample_csv_data):
        """测试数据导入和验证工作流程"""
        # 创建管理器
        targets_manager = CalibrationTargets(storage_path=temp_dir)
        validator = DataValidator()
        
        # 导入数据
        count = targets_manager.load_targets_from_file(sample_csv_data)
        assert count == 4
        
        # 验证数据
        all_targets = targets_manager.get_all_targets()
        quality_report = validator.validate_targets(all_targets)
        
        # 检查验证结果
        assert quality_report.total_targets == 4
        assert quality_report.overall_score > 0
        
        # 应该有一些验证结果（警告或错误）
        validation_results = quality_report.validation_results
        assert len(validation_results) >= 0  # 可能没有问题
        
        # 检查质量指标
        quality_metrics = quality_report.quality_metrics
        assert 'completeness_score' in quality_metrics
        assert 'error_rate' in quality_metrics
        assert 'warning_rate' in quality_metrics
        assert 'data_coverage' in quality_metrics
        assert 'quality_distribution' in quality_metrics
        
        # 检查改进建议
        recommendations = quality_report.recommendations
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
    
    def test_weight_optimization_workflow(self, temp_dir, sample_csv_data):
        """测试权重优化工作流程"""
        # 设置管理器
        targets_manager = CalibrationTargets(storage_path=temp_dir)
        targets_manager.load_targets_from_file(sample_csv_data)
        
        weight_manager = TargetWeightManager(storage_path=temp_dir)
        all_targets = targets_manager.get_all_targets()
        
        # 测试不同的权重优化方法
        methods = ["inverse_variance", "equal", "adaptive"]
        
        # 模拟模型输出用于自适应权重
        model_outputs = {
            "adenoma_prevalence_50-54_male": 0.14,
            "adenoma_prevalence_50-54_female": 0.13,
            "cancer_incidence_60-64_male": 0.009,
            "cancer_mortality_70-74_male": 0.0025
        }
        
        for method in methods:
            if method == "adaptive":
                weights = weight_manager.optimize_weights(all_targets, model_outputs, method)
            else:
                weights = weight_manager.optimize_weights(all_targets, optimization_method=method)
            
            assert len(weights) == 4
            for weight in weights.values():
                assert weight > 0
        
        # 测试敏感性分析
        sensitivity_results = weight_manager.perform_sensitivity_analysis(all_targets)
        
        assert 'base_weights' in sensitivity_results
        assert 'variations' in sensitivity_results
        assert 'sensitivity_scores' in sensitivity_results
        
        # 创建自定义权重方案
        custom_weights = {
            'adenoma_prevalence': 1.5,
            'cancer_incidence': 2.5,
            'cancer_mortality': 3.5
        }
        
        weight_manager.create_weight_scheme(
            "custom_scheme", custom_weights, "自定义方案", "test_user"
        )
        
        weight_manager.set_current_scheme("custom_scheme")
        
        # 使用自定义方案计算权重
        custom_auto_weights = weight_manager.calculate_automatic_weights(all_targets)
        assert len(custom_auto_weights) == 4
    
    def test_comparison_analysis_workflow(self, temp_dir, sample_csv_data):
        """测试比较分析工作流程"""
        # 设置
        targets_manager = CalibrationTargets(storage_path=temp_dir)
        targets_manager.load_targets_from_file(sample_csv_data)
        
        comparator = TargetComparator(targets_manager)
        
        # 测试不同质量的模型输出
        test_scenarios = [
            {
                "name": "perfect_match",
                "outputs": {
                    "adenoma_prevalence_50-54_male": 0.15,
                    "adenoma_prevalence_50-54_female": 0.12,
                    "cancer_incidence_60-64_male": 0.008,
                    "cancer_mortality_70-74_male": 0.003
                }
            },
            {
                "name": "good_match",
                "outputs": {
                    "adenoma_prevalence_50-54_male": 0.14,
                    "adenoma_prevalence_50-54_female": 0.13,
                    "cancer_incidence_60-64_male": 0.009,
                    "cancer_mortality_70-74_male": 0.0028
                }
            },
            {
                "name": "poor_match",
                "outputs": {
                    "adenoma_prevalence_50-54_male": 0.10,
                    "adenoma_prevalence_50-54_female": 0.18,
                    "cancer_incidence_60-64_male": 0.015,
                    "cancer_mortality_70-74_male": 0.001
                }
            }
        ]
        
        for scenario in test_scenarios:
            results = comparator.compare_outputs(scenario["outputs"])
            
            # 检查结果结构
            assert 'overall_fit' in results
            assert 'target_specific_fit' in results
            assert 'statistical_tests' in results
            assert 'recommendations' in results
            
            # 检查总体拟合质量
            overall_quality = results['overall_fit']['overall_quality']
            assert overall_quality in ["优秀", "良好", "中等", "较差"]
            
            # 完美匹配应该有较好的质量（但由于数据量少可能不是优秀）
            if scenario["name"] == "perfect_match":
                # 完美匹配的MAPE应该很小
                assert results['overall_fit']['mean_mape'] < 5.0
            
            # 较差匹配应该有较低的质量
            elif scenario["name"] == "poor_match":
                assert results['overall_fit']['mean_mape'] > 10.0
    
    def test_version_control_workflow(self, temp_dir, sample_csv_data):
        """测试版本控制工作流程"""
        targets_manager = CalibrationTargets(storage_path=temp_dir)
        
        # 初始数据加载
        targets_manager.load_targets_from_file(sample_csv_data)
        initial_count = targets_manager.metadata.total_targets
        
        # 创建初始版本快照
        v1_id = targets_manager.create_version_snapshot("初始版本")
        assert v1_id.startswith("v1_")
        assert len(targets_manager.version_history) == 1
        
        # 添加新目标
        new_target = CalibrationTarget(
            target_type=TargetType.SURVIVAL_RATE,
            age_group="65-69",
            gender="female",
            value=0.85,
            data_quality=DataQuality.MEDIUM
        )
        targets_manager.add_target(new_target)
        
        # 创建第二个版本
        v2_id = targets_manager.create_version_snapshot("添加生存率目标")
        assert v2_id.startswith("v2_")
        assert len(targets_manager.version_history) == 2
        assert targets_manager.metadata.total_targets == initial_count + 1
        
        # 恢复到第一个版本
        targets_manager.restore_from_version(v1_id)
        assert targets_manager.metadata.total_targets == initial_count
        
        # 验证生存率目标已被移除
        survival_target = targets_manager.get_target(
            TargetType.SURVIVAL_RATE, "65-69", "female"
        )
        assert survival_target is None
        
        # 创建备份
        backup_path = targets_manager.create_backup("test_backup")
        assert Path(backup_path).exists()
        
        # 清空数据并从备份恢复
        targets_manager.targets.clear()
        targets_manager.metadata.total_targets = 0
        
        targets_manager.restore_from_backup(backup_path)
        assert targets_manager.metadata.total_targets == initial_count
    
    def test_age_gender_modeling_workflow(self, temp_dir):
        """测试年龄性别建模工作流程"""
        targets_manager = CalibrationTargets(storage_path=temp_dir)
        
        # 创建年龄性别分层数据
        age_groups = ["50-54", "55-59", "60-64", "65-69", "70-74"]
        genders = ["male", "female"]
        
        # 模拟腺瘤患病率随年龄增长的趋势
        base_prevalence = {"male": 0.10, "female": 0.08}
        age_increment = 0.02
        
        for i, age_group in enumerate(age_groups):
            for gender in genders:
                value = base_prevalence[gender] + i * age_increment
                target = CalibrationTarget(
                    target_type=TargetType.ADENOMA_PREVALENCE,
                    age_group=age_group,
                    gender=gender,
                    value=value,
                    standard_error=value * 0.1,
                    data_quality=DataQuality.HIGH
                )
                targets_manager.add_target(target)
        
        # 测试性别差异建模
        gender_results = targets_manager.model_gender_specific_differences(
            TargetType.ADENOMA_PREVALENCE, age_groups
        )
        
        assert 'male_values' in gender_results
        assert 'female_values' in gender_results
        assert 'gender_ratios' in gender_results
        assert 'age_trends' in gender_results
        
        # 验证性别比率
        for age_group in age_groups:
            if age_group in gender_results['gender_ratios']:
                ratio = gender_results['gender_ratios'][age_group]
                assert ratio > 1.0  # 男性患病率应该高于女性
        
        # 测试年龄性别交互效应建模
        interaction_results = targets_manager.model_age_gender_interactions(
            TargetType.ADENOMA_PREVALENCE
        )
        
        assert 'error' not in interaction_results
        assert 'coefficients' in interaction_results
        assert 'model_fit' in interaction_results
        
        # 年龄效应应该为正（随年龄增长）
        age_effect = interaction_results['coefficients']['age_effect']
        assert age_effect > 0
        
        # 模型拟合应该较好
        r_squared = interaction_results['model_fit']['r_squared']
        assert r_squared > 0.8  # 线性趋势应该拟合得很好
        
        # 测试年龄插值
        target_ages = [52, 57, 62, 67, 72]
        interpolated_male = targets_manager.interpolate_age_specific_targets(
            TargetType.ADENOMA_PREVALENCE, "male", target_ages
        )
        
        assert len(interpolated_male) == 5
        
        # 验证插值结果的单调性（应该随年龄增长）
        ages_sorted = sorted(interpolated_male.keys())
        values_sorted = [interpolated_male[age] for age in ages_sorted]
        
        for i in range(1, len(values_sorted)):
            assert values_sorted[i] >= values_sorted[i-1]  # 单调递增
    
    def test_error_handling_and_robustness(self, temp_dir):
        """测试错误处理和鲁棒性"""
        targets_manager = CalibrationTargets(storage_path=temp_dir)
        
        # 测试加载不存在的文件
        with pytest.raises(FileNotFoundError):
            targets_manager.load_targets_from_file("nonexistent_file.csv")
        
        # 测试加载格式错误的数据
        bad_csv_file = Path(temp_dir) / "bad_data.csv"
        with open(bad_csv_file, 'w') as f:
            f.write("invalid,csv,data\n1,2,3\n")
        
        with pytest.raises(ValueError):
            targets_manager.load_targets_from_file(bad_csv_file)
        
        # 测试空数据的处理
        empty_targets_manager = CalibrationTargets(storage_path=temp_dir)
        
        # 空数据的统计
        stats = empty_targets_manager.get_summary_statistics()
        assert stats['total_targets'] == 0
        
        # 空数据的比较
        comparator = TargetComparator(empty_targets_manager)
        results = comparator.compare_outputs({"test": 1.0})
        
        # 应该没有匹配的输出
        for target_fit in results['target_specific_fit'].values():
            assert 'error' in target_fit
        
        # 测试权重管理器的错误处理
        weight_manager = TargetWeightManager(storage_path=temp_dir)
        
        # 设置不存在的方案
        with pytest.raises(ValueError):
            weight_manager.set_current_scheme("nonexistent")
        
        # 空目标列表的权重计算
        empty_weights = weight_manager.calculate_automatic_weights([])
        assert len(empty_weights) == 0
