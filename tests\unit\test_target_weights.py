"""
校准目标权重管理器单元测试
"""

import pytest
import tempfile
import json
from pathlib import Path
from datetime import datetime

from src.calibration.target_weights import TargetWeightManager, WeightScheme
from src.calibration.targets import CalibrationTarget, TargetType, DataQuality


class TestWeightScheme:
    """测试WeightScheme数据类"""
    
    def test_weight_scheme_creation(self):
        """测试权重方案创建"""
        weights = {
            'adenoma_prevalence': 1.0,
            'cancer_incidence': 2.0,
            'cancer_mortality': 3.0
        }
        
        scheme = WeightScheme(
            name="test_scheme",
            description="测试权重方案",
            weights=weights,
            created_time=datetime.now(),
            author="test_user"
        )
        
        assert scheme.name == "test_scheme"
        assert scheme.description == "测试权重方案"
        assert scheme.weights == weights
        assert scheme.author == "test_user"
    
    def test_scheme_to_dict(self):
        """测试方案转换为字典"""
        weights = {'test': 1.0}
        scheme = WeightScheme(
            name="test",
            description="test",
            weights=weights,
            created_time=datetime(2023, 1, 1),
            author="test"
        )
        
        scheme_dict = scheme.to_dict()
        
        assert scheme_dict['name'] == "test"
        assert scheme_dict['weights'] == weights
        assert scheme_dict['created_time'] == "2023-01-01T00:00:00"
    
    def test_scheme_from_dict(self):
        """测试从字典创建方案"""
        data = {
            'name': 'test',
            'description': 'test',
            'weights': {'test': 1.0},
            'created_time': '2023-01-01T00:00:00',
            'author': 'test'
        }
        
        scheme = WeightScheme.from_dict(data)
        
        assert scheme.name == 'test'
        assert scheme.weights == {'test': 1.0}
        assert scheme.created_time == datetime(2023, 1, 1)


class TestTargetWeightManager:
    """测试TargetWeightManager类"""
    
    @pytest.fixture
    def weight_manager(self):
        """创建临时权重管理器"""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = TargetWeightManager(storage_path=temp_dir)
            yield manager
    
    @pytest.fixture
    def sample_targets(self):
        """创建样本目标数据"""
        targets = [
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="male",
                value=0.15,
                standard_error=0.02,
                sample_size=1000,
                data_quality=DataQuality.HIGH,
                priority=1
            ),
            CalibrationTarget(
                target_type=TargetType.CANCER_INCIDENCE,
                age_group="60-64",
                gender="female",
                value=0.008,
                standard_error=0.001,
                sample_size=500,
                data_quality=DataQuality.MEDIUM,
                priority=2
            ),
            CalibrationTarget(
                target_type=TargetType.CANCER_MORTALITY,
                age_group="70-74",
                gender="male",
                value=0.003,
                standard_error=0.0005,
                sample_size=200,
                data_quality=DataQuality.LOW,
                priority=3
            )
        ]
        return targets
    
    def test_initialization(self, weight_manager):
        """测试初始化"""
        assert "default" in weight_manager.weight_schemes
        assert weight_manager.current_scheme == "default"
        
        default_scheme = weight_manager.weight_schemes["default"]
        assert default_scheme.name == "default"
        assert "adenoma_prevalence" in default_scheme.weights
    
    def test_create_weight_scheme(self, weight_manager):
        """测试创建权重方案"""
        weights = {
            'adenoma_prevalence': 1.5,
            'cancer_incidence': 2.5,
            'cancer_mortality': 3.5
        }
        
        weight_manager.create_weight_scheme(
            "custom_scheme",
            weights,
            "自定义权重方案",
            "test_user"
        )
        
        assert "custom_scheme" in weight_manager.weight_schemes
        scheme = weight_manager.weight_schemes["custom_scheme"]
        assert scheme.weights == weights
        assert scheme.description == "自定义权重方案"
        assert scheme.author == "test_user"
    
    def test_set_current_scheme(self, weight_manager):
        """测试设置当前权重方案"""
        # 创建新方案
        weights = {'test': 1.0}
        weight_manager.create_weight_scheme("test_scheme", weights)
        
        # 设置为当前方案
        weight_manager.set_current_scheme("test_scheme")
        assert weight_manager.current_scheme == "test_scheme"
        
        # 测试设置不存在的方案
        with pytest.raises(ValueError):
            weight_manager.set_current_scheme("nonexistent_scheme")
    
    def test_calculate_automatic_weights(self, weight_manager, sample_targets):
        """测试自动权重计算"""
        weights = weight_manager.calculate_automatic_weights(sample_targets)
        
        assert len(weights) == 3
        
        # 检查权重键格式
        expected_keys = [
            "adenoma_prevalence_50-54_male",
            "cancer_incidence_60-64_female",
            "cancer_mortality_70-74_male"
        ]
        
        for key in expected_keys:
            assert key in weights
            assert weights[key] > 0
        
        # 高质量数据应该有更高的权重
        high_quality_key = "adenoma_prevalence_50-54_male"
        low_quality_key = "cancer_mortality_70-74_male"
        assert weights[high_quality_key] > weights[low_quality_key]
    
    def test_inverse_variance_weights(self, weight_manager, sample_targets):
        """测试逆方差权重"""
        weights = weight_manager.optimize_weights(
            sample_targets, 
            optimization_method="inverse_variance"
        )
        
        assert len(weights) == 3
        
        # 标准误差小的目标应该有更高的权重
        keys = list(weights.keys())
        # 找到标准误差最小的目标（cancer_mortality: 0.0005）
        mortality_key = "cancer_mortality_70-74_male"
        adenoma_key = "adenoma_prevalence_50-54_male"  # 标准误差0.02
        
        assert weights[mortality_key] > weights[adenoma_key]
    
    def test_equal_weights(self, weight_manager, sample_targets):
        """测试等权重"""
        weights = weight_manager.optimize_weights(
            sample_targets,
            optimization_method="equal"
        )
        
        assert len(weights) == 3
        
        # 所有权重应该相等
        weight_values = list(weights.values())
        assert all(w == weight_values[0] for w in weight_values)
        assert weight_values[0] == 1.0
    
    def test_adaptive_weights(self, weight_manager, sample_targets):
        """测试自适应权重"""
        # 模拟模型输出（故意让某些目标有较大误差）
        model_outputs = {
            "adenoma_prevalence_50-54_male": 0.10,  # 目标0.15，误差较大
            "cancer_incidence_60-64_female": 0.008,  # 目标0.008，误差小
            "cancer_mortality_70-74_male": 0.002    # 目标0.003，误差中等
        }
        
        weights = weight_manager.optimize_weights(
            sample_targets,
            model_outputs,
            optimization_method="adaptive"
        )
        
        assert len(weights) == 3
        
        # 误差大的目标应该有更高的权重
        adenoma_key = "adenoma_prevalence_50-54_male"
        cancer_key = "cancer_incidence_60-64_female"
        
        # 腺瘤患病率误差最大，应该权重最高
        assert weights[adenoma_key] > weights[cancer_key]
    
    def test_perform_sensitivity_analysis(self, weight_manager, sample_targets):
        """测试敏感性分析"""
        results = weight_manager.perform_sensitivity_analysis(sample_targets)
        
        assert 'base_weights' in results
        assert 'variations' in results
        assert 'sensitivity_scores' in results
        
        # 检查基础权重
        base_weights = results['base_weights']
        assert len(base_weights) == 3
        
        # 检查变化
        variations = results['variations']
        assert len(variations) == 3
        
        for target_key, var_dict in variations.items():
            assert len(var_dict) == 6  # 默认6个变化点
            assert '1.0' in var_dict  # 基准变化
            assert '0.5' in var_dict
            assert '2.0' in var_dict
        
        # 检查敏感性分数
        sensitivity_scores = results['sensitivity_scores']
        assert len(sensitivity_scores) == 3
        
        for score in sensitivity_scores.values():
            assert score >= 0  # 敏感性分数应该非负
    
    def test_save_and_load_schemes(self, weight_manager):
        """测试保存和加载权重方案"""
        # 创建自定义方案
        custom_weights = {
            'adenoma_prevalence': 1.5,
            'cancer_incidence': 2.5
        }
        weight_manager.create_weight_scheme(
            "custom", custom_weights, "自定义方案", "test"
        )
        weight_manager.set_current_scheme("custom")
        
        # 保存到临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            weight_manager.save_schemes(temp_file)
            
            # 创建新管理器并加载
            new_manager = TargetWeightManager()
            new_manager.load_schemes(temp_file)
            
            # 验证数据
            assert "custom" in new_manager.weight_schemes
            assert new_manager.current_scheme == "custom"
            
            custom_scheme = new_manager.weight_schemes["custom"]
            assert custom_scheme.weights == custom_weights
            assert custom_scheme.description == "自定义方案"
            
        finally:
            Path(temp_file).unlink(missing_ok=True)
    
    def test_weight_calculation_with_missing_data(self, weight_manager):
        """测试缺失数据的权重计算"""
        # 创建有缺失数据的目标
        targets = [
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="male",
                value=0.15,
                standard_error=None,  # 缺失标准误差
                sample_size=None,     # 缺失样本量
                data_quality=DataQuality.UNCERTAIN
            )
        ]
        
        weights = weight_manager.calculate_automatic_weights(targets)
        
        assert len(weights) == 1
        key = "adenoma_prevalence_50-54_male"
        assert key in weights
        assert weights[key] > 0  # 即使有缺失数据，权重也应该为正
    
    def test_weight_normalization(self, weight_manager, sample_targets):
        """测试权重标准化"""
        weights = weight_manager.calculate_automatic_weights(sample_targets)
        
        # 权重总和应该等于目标数量（标准化后）
        total_weight = sum(weights.values())
        expected_total = len(sample_targets)
        
        # 允许小的数值误差
        assert abs(total_weight - expected_total) < 0.001
    
    def test_quality_based_weighting(self, weight_manager):
        """测试基于质量的权重分配"""
        targets = [
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="male",
                value=0.15,
                data_quality=DataQuality.HIGH
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="female",
                value=0.12,
                data_quality=DataQuality.LOW
            )
        ]
        
        weights = weight_manager.calculate_automatic_weights(targets)
        
        high_quality_key = "adenoma_prevalence_50-54_male"
        low_quality_key = "adenoma_prevalence_50-54_female"
        
        # 高质量数据应该有更高的权重
        assert weights[high_quality_key] > weights[low_quality_key]
    
    def test_priority_based_weighting(self, weight_manager):
        """测试基于优先级的权重分配"""
        targets = [
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="male",
                value=0.15,
                priority=1  # 高优先级
            ),
            CalibrationTarget(
                target_type=TargetType.ADENOMA_PREVALENCE,
                age_group="50-54",
                gender="female",
                value=0.12,
                priority=5  # 低优先级
            )
        ]
        
        weights = weight_manager.calculate_automatic_weights(targets)
        
        high_priority_key = "adenoma_prevalence_50-54_male"
        low_priority_key = "adenoma_prevalence_50-54_female"
        
        # 高优先级应该有更高的权重
        assert weights[high_priority_key] > weights[low_priority_key]
