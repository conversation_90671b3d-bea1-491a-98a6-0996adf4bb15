"""
校准目标可视化组件
实现目标值对比图表、分层可视化和交互式编辑功能
"""

import logging
from typing import Dict, List, Optional, Any
import numpy as np
import pandas as pd

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QTableWidget, 
    QTableWidgetItem, QPushButton, QComboBox, QLabel, QSpinBox,
    QDoubleSpinBox, QGroupBox, QSplitter, QTextEdit, QCheckBox,
    QProgressBar, QMessageBox, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QColor

# 可选的绘图依赖
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import seaborn as sns
    HAS_PLOTTING = True
except ImportError:
    HAS_PLOTTING = False
    FigureCanvas = QWidget
    Figure = object

from src.calibration.targets import CalibrationTargets, CalibrationTarget, TargetType, DataQuality
from src.calibration.target_comparison import TargetComparator
from src.calibration.data_validator import DataValidator

logger = logging.getLogger(__name__)


class CalibrationTargetsWidget(QWidget):
    """校准目标可视化组件"""
    
    # 信号定义
    targets_updated = pyqtSignal()
    comparison_completed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        """初始化组件"""
        super().__init__(parent)
        
        # 数据管理
        self.targets = CalibrationTargets()
        self.comparator = TargetComparator(self.targets)
        self.validator = DataValidator()
        
        # 当前模型输出数据
        self.current_model_outputs: Dict[str, float] = {}
        
        # 初始化UI
        self._init_ui()
        
        logger.info("校准目标可视化组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建工具栏
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)
        
        # 创建主要内容区域
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：数据管理和控制
        left_panel = self._create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # 右侧：可视化和分析
        right_panel = self._create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割比例
        main_splitter.setSizes([300, 700])
        layout.addWidget(main_splitter)
        
        # 状态栏
        self.status_bar = QLabel("就绪")
        layout.addWidget(self.status_bar)
    
    def _create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)
        
        # 数据导入按钮
        self.import_btn = QPushButton("导入目标数据")
        self.import_btn.clicked.connect(self._import_targets)
        layout.addWidget(self.import_btn)
        
        # 数据导出按钮
        self.export_btn = QPushButton("导出数据")
        self.export_btn.clicked.connect(self._export_targets)
        layout.addWidget(self.export_btn)
        
        # 数据验证按钮
        self.validate_btn = QPushButton("验证数据质量")
        self.validate_btn.clicked.connect(self._validate_data)
        layout.addWidget(self.validate_btn)
        
        # 比较分析按钮
        self.compare_btn = QPushButton("比较分析")
        self.compare_btn.clicked.connect(self._perform_comparison)
        layout.addWidget(self.compare_btn)
        
        layout.addStretch()
        
        return toolbar
    
    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 目标类型选择
        type_group = QGroupBox("目标类型筛选")
        type_layout = QVBoxLayout(type_group)
        
        self.type_checkboxes = {}
        for target_type in TargetType:
            checkbox = QCheckBox(target_type.value)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self._update_display)
            self.type_checkboxes[target_type] = checkbox
            type_layout.addWidget(checkbox)
        
        layout.addWidget(type_group)
        
        # 年龄性别筛选
        filter_group = QGroupBox("筛选条件")
        filter_layout = QVBoxLayout(filter_group)
        
        # 性别选择
        gender_layout = QHBoxLayout()
        gender_layout.addWidget(QLabel("性别:"))
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["全部", "male", "female"])
        self.gender_combo.currentTextChanged.connect(self._update_display)
        gender_layout.addWidget(self.gender_combo)
        filter_layout.addLayout(gender_layout)
        
        # 年龄范围
        age_layout = QHBoxLayout()
        age_layout.addWidget(QLabel("年龄范围:"))
        self.min_age_spin = QSpinBox()
        self.min_age_spin.setRange(0, 100)
        self.min_age_spin.setValue(50)
        self.min_age_spin.valueChanged.connect(self._update_display)
        age_layout.addWidget(self.min_age_spin)
        
        age_layout.addWidget(QLabel("-"))
        self.max_age_spin = QSpinBox()
        self.max_age_spin.setRange(0, 100)
        self.max_age_spin.setValue(80)
        self.max_age_spin.valueChanged.connect(self._update_display)
        age_layout.addWidget(self.max_age_spin)
        
        filter_layout.addLayout(age_layout)
        layout.addWidget(filter_group)
        
        # 数据统计信息
        stats_group = QGroupBox("数据统计")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(150)
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text)
        
        layout.addWidget(stats_group)
        
        # 质量报告
        quality_group = QGroupBox("质量报告")
        quality_layout = QVBoxLayout(quality_group)
        
        self.quality_text = QTextEdit()
        self.quality_text.setMaximumHeight(150)
        self.quality_text.setReadOnly(True)
        quality_layout.addWidget(self.quality_text)
        
        layout.addWidget(quality_group)
        
        layout.addStretch()
        return panel
    
    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 数据表格标签页
        self.table_tab = self._create_table_tab()
        self.tab_widget.addTab(self.table_tab, "数据表格")
        
        # 可视化标签页
        if HAS_PLOTTING:
            self.plot_tab = self._create_plot_tab()
            self.tab_widget.addTab(self.plot_tab, "可视化图表")
        
        # 比较分析标签页
        self.comparison_tab = self._create_comparison_tab()
        self.tab_widget.addTab(self.comparison_tab, "比较分析")
        
        layout.addWidget(self.tab_widget)
        
        return panel
    
    def _create_table_tab(self) -> QWidget:
        """创建数据表格标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建表格
        self.targets_table = QTableWidget()
        self.targets_table.setAlternatingRowColors(True)
        self.targets_table.setSortingEnabled(True)
        
        # 设置表格列
        columns = [
            "目标类型", "年龄组", "性别", "目标值", "标准误差", 
            "置信区间", "样本量", "数据源", "发表年份", "数据质量", "权重", "优先级"
        ]
        self.targets_table.setColumnCount(len(columns))
        self.targets_table.setHorizontalHeaderLabels(columns)
        
        # 允许编辑
        self.targets_table.itemChanged.connect(self._on_table_item_changed)
        
        layout.addWidget(self.targets_table)
        
        return tab
    
    def _create_plot_tab(self) -> QWidget:
        """创建可视化标签页"""
        if not HAS_PLOTTING:
            tab = QWidget()
            layout = QVBoxLayout(tab)
            layout.addWidget(QLabel("绘图功能需要安装matplotlib和seaborn"))
            return tab
        
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 绘图控制
        plot_controls = QHBoxLayout()
        
        plot_controls.addWidget(QLabel("图表类型:"))
        self.plot_type_combo = QComboBox()
        self.plot_type_combo.addItems([
            "目标值对比", "年龄趋势", "性别差异", "数据质量分布", "置信区间图"
        ])
        self.plot_type_combo.currentTextChanged.connect(self._update_plot)
        plot_controls.addWidget(self.plot_type_combo)
        
        self.update_plot_btn = QPushButton("更新图表")
        self.update_plot_btn.clicked.connect(self._update_plot)
        plot_controls.addWidget(self.update_plot_btn)
        
        plot_controls.addStretch()
        layout.addLayout(plot_controls)
        
        # 创建matplotlib画布
        self.figure = Figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        return tab
    
    def _create_comparison_tab(self) -> QWidget:
        """创建比较分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 模型输出输入区域
        input_group = QGroupBox("模型输出数据")
        input_layout = QVBoxLayout(input_group)
        
        input_controls = QHBoxLayout()
        self.load_outputs_btn = QPushButton("加载模型输出")
        self.load_outputs_btn.clicked.connect(self._load_model_outputs)
        input_controls.addWidget(self.load_outputs_btn)
        
        self.clear_outputs_btn = QPushButton("清空数据")
        self.clear_outputs_btn.clicked.connect(self._clear_model_outputs)
        input_controls.addWidget(self.clear_outputs_btn)
        
        input_controls.addStretch()
        input_layout.addLayout(input_controls)
        
        # 模型输出数据显示
        self.outputs_table = QTableWidget()
        self.outputs_table.setMaximumHeight(200)
        input_layout.addWidget(self.outputs_table)
        
        layout.addWidget(input_group)
        
        # 比较结果显示
        results_group = QGroupBox("比较结果")
        results_layout = QVBoxLayout(results_group)
        
        self.comparison_results = QTextEdit()
        self.comparison_results.setReadOnly(True)
        results_layout.addWidget(self.comparison_results)
        
        layout.addWidget(results_group)
        
        return tab
    
    def _import_targets(self):
        """导入目标数据"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入校准目标数据", "", 
            "CSV文件 (*.csv);;Excel文件 (*.xlsx);;JSON文件 (*.json);;YAML文件 (*.yaml)"
        )
        
        if file_path:
            try:
                count = self.targets.load_targets_from_file(file_path)
                self.comparator = TargetComparator(self.targets)  # 更新比较器
                self._update_display()
                self.status_bar.setText(f"成功导入 {count} 个校准目标")
                self.targets_updated.emit()
            except Exception as e:
                QMessageBox.critical(self, "导入错误", f"导入失败: {str(e)}")
                logger.error(f"导入目标数据失败: {e}")
    
    def _export_targets(self):
        """导出目标数据"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出校准目标数据", "", 
            "JSON文件 (*.json);;CSV文件 (*.csv)"
        )
        
        if file_path:
            try:
                self.targets.save_to_file(file_path)
                self.status_bar.setText(f"数据已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "导出错误", f"导出失败: {str(e)}")
                logger.error(f"导出目标数据失败: {e}")
    
    def _validate_data(self):
        """验证数据质量"""
        try:
            all_targets = self.targets.get_all_targets()
            if not all_targets:
                QMessageBox.information(self, "提示", "没有可验证的数据")
                return
            
            report = self.validator.validate_targets(all_targets)
            
            # 显示质量报告
            quality_text = f"总体质量分数: {report.overall_score:.2f}\n"
            quality_text += f"总目标数: {report.total_targets}\n"
            quality_text += f"错误数: {sum(1 for r in report.validation_results if r.status == 'error')}\n"
            quality_text += f"警告数: {sum(1 for r in report.validation_results if r.status == 'warning')}\n\n"
            
            quality_text += "改进建议:\n"
            for rec in report.recommendations:
                quality_text += f"• {rec}\n"
            
            self.quality_text.setText(quality_text)
            self.status_bar.setText("数据质量验证完成")
            
        except Exception as e:
            QMessageBox.critical(self, "验证错误", f"验证失败: {str(e)}")
            logger.error(f"数据验证失败: {e}")
    
    def _perform_comparison(self):
        """执行比较分析"""
        if not self.current_model_outputs:
            QMessageBox.information(self, "提示", "请先加载模型输出数据")
            return
        
        try:
            results = self.comparator.compare_outputs(self.current_model_outputs)
            
            # 显示比较结果
            self._display_comparison_results(results)
            self.comparison_completed.emit(results)
            self.status_bar.setText("比较分析完成")
            
        except Exception as e:
            QMessageBox.critical(self, "比较错误", f"比较分析失败: {str(e)}")
            logger.error(f"比较分析失败: {e}")
    
    def _load_model_outputs(self):
        """加载模型输出数据"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载模型输出数据", "", 
            "CSV文件 (*.csv);;JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path)
                    # 假设CSV有'target_key'和'value'列
                    self.current_model_outputs = dict(zip(df['target_key'], df['value']))
                elif file_path.endswith('.json'):
                    import json
                    with open(file_path, 'r') as f:
                        self.current_model_outputs = json.load(f)
                
                self._update_outputs_table()
                self.status_bar.setText(f"加载了 {len(self.current_model_outputs)} 个模型输出")
                
            except Exception as e:
                QMessageBox.critical(self, "加载错误", f"加载失败: {str(e)}")
                logger.error(f"加载模型输出失败: {e}")
    
    def _clear_model_outputs(self):
        """清空模型输出数据"""
        self.current_model_outputs.clear()
        self._update_outputs_table()
        self.comparison_results.clear()
        self.status_bar.setText("模型输出数据已清空")
    
    def _update_display(self):
        """更新显示"""
        self._update_table()
        self._update_statistics()
        if HAS_PLOTTING:
            self._update_plot()
    
    def _update_table(self):
        """更新数据表格"""
        # 获取筛选后的目标
        filtered_targets = self._get_filtered_targets()
        
        # 设置表格行数
        self.targets_table.setRowCount(len(filtered_targets))
        
        # 填充数据
        for row, target in enumerate(filtered_targets):
            items = [
                target.target_type.value,
                target.age_group,
                target.gender,
                f"{target.value:.4f}",
                f"{target.standard_error:.4f}" if target.standard_error else "",
                f"{target.confidence_interval}" if target.confidence_interval else "",
                str(target.sample_size) if target.sample_size else "",
                target.data_source,
                str(target.publication_year),
                target.data_quality.value,
                f"{target.weight:.2f}",
                str(target.priority)
            ]
            
            for col, item in enumerate(items):
                self.targets_table.setItem(row, col, QTableWidgetItem(str(item)))
        
        # 调整列宽
        self.targets_table.resizeColumnsToContents()
    
    def _update_statistics(self):
        """更新统计信息"""
        stats = self.targets.get_summary_statistics()
        
        stats_text = f"总目标数: {stats['total_targets']}\n"
        stats_text += f"数据源数: {stats['data_sources']}\n"
        stats_text += f"版本: {stats['version']}\n"
        stats_text += f"最后修改: {stats['last_modified'][:19]}\n\n"
        
        stats_text += "目标类型分布:\n"
        for target_type, count in stats['target_types'].items():
            stats_text += f"  {target_type}: {count}\n"
        
        stats_text += f"\n年龄组: {', '.join(stats['age_groups'])}\n"
        stats_text += f"性别: {', '.join(stats['genders'])}\n"
        
        self.stats_text.setText(stats_text)
    
    def _get_filtered_targets(self) -> List[CalibrationTarget]:
        """获取筛选后的目标"""
        all_targets = self.targets.get_all_targets()
        filtered = []
        
        # 获取筛选条件
        selected_types = [t for t, cb in self.type_checkboxes.items() if cb.isChecked()]
        selected_gender = self.gender_combo.currentText()
        min_age = self.min_age_spin.value()
        max_age = self.max_age_spin.value()
        
        for target in all_targets:
            # 类型筛选
            if target.target_type not in selected_types:
                continue
            
            # 性别筛选
            if selected_gender != "全部" and target.gender != selected_gender:
                continue
            
            # 年龄筛选（简单实现，基于年龄组中点）
            age_mid = self._get_age_midpoint(target.age_group)
            if not (min_age <= age_mid <= max_age):
                continue
            
            filtered.append(target)
        
        return filtered
    
    def _get_age_midpoint(self, age_group: str) -> float:
        """获取年龄组中点"""
        if '-' in age_group:
            start, end = age_group.split('-')
            return (int(start) + int(end)) / 2
        elif '+' in age_group:
            return int(age_group.replace('+', '')) + 5
        else:
            return float(age_group)

    def _update_plot(self):
        """更新图表"""
        if not HAS_PLOTTING:
            return

        self.figure.clear()

        plot_type = self.plot_type_combo.currentText()
        filtered_targets = self._get_filtered_targets()

        if not filtered_targets:
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, '没有数据可显示', ha='center', va='center', transform=ax.transAxes)
            self.canvas.draw()
            return

        try:
            if plot_type == "目标值对比":
                self._plot_target_comparison(filtered_targets)
            elif plot_type == "年龄趋势":
                self._plot_age_trends(filtered_targets)
            elif plot_type == "性别差异":
                self._plot_gender_differences(filtered_targets)
            elif plot_type == "数据质量分布":
                self._plot_quality_distribution(filtered_targets)
            elif plot_type == "置信区间图":
                self._plot_confidence_intervals(filtered_targets)

            self.figure.tight_layout()
            self.canvas.draw()

        except Exception as e:
            logger.error(f"绘图失败: {e}")
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'绘图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes)
            self.canvas.draw()

    def _plot_target_comparison(self, targets: List[CalibrationTarget]):
        """绘制目标值对比图"""
        # 按目标类型分组
        type_data = {}
        for target in targets:
            if target.target_type not in type_data:
                type_data[target.target_type] = []
            type_data[target.target_type].append(target.value)

        if not type_data:
            return

        ax = self.figure.add_subplot(111)

        # 创建箱线图
        data_values = []
        labels = []
        for target_type, values in type_data.items():
            data_values.append(values)
            labels.append(target_type.value)

        bp = ax.boxplot(data_values, labels=labels, patch_artist=True)

        # 设置颜色
        colors = plt.cm.Set3(np.linspace(0, 1, len(bp['boxes'])))
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)

        ax.set_title('校准目标值分布对比')
        ax.set_ylabel('目标值')
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    def _plot_age_trends(self, targets: List[CalibrationTarget]):
        """绘制年龄趋势图"""
        # 按目标类型和性别分组
        trend_data = {}
        for target in targets:
            key = f"{target.target_type.value}_{target.gender}"
            if key not in trend_data:
                trend_data[key] = []

            age_mid = self._get_age_midpoint(target.age_group)
            trend_data[key].append((age_mid, target.value))

        if not trend_data:
            return

        ax = self.figure.add_subplot(111)

        colors = plt.cm.tab10(np.linspace(0, 1, len(trend_data)))

        for i, (key, data) in enumerate(trend_data.items()):
            if len(data) < 2:
                continue

            data.sort(key=lambda x: x[0])  # 按年龄排序
            ages, values = zip(*data)

            ax.plot(ages, values, 'o-', color=colors[i], label=key, linewidth=2, markersize=6)

        ax.set_title('校准目标年龄趋势')
        ax.set_xlabel('年龄')
        ax.set_ylabel('目标值')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)

    def _plot_gender_differences(self, targets: List[CalibrationTarget]):
        """绘制性别差异图"""
        # 按目标类型和年龄组分组
        gender_data = {}
        for target in targets:
            key = f"{target.target_type.value}_{target.age_group}"
            if key not in gender_data:
                gender_data[key] = {}
            gender_data[key][target.gender] = target.value

        # 筛选有男女数据的组
        complete_data = {}
        for key, data in gender_data.items():
            if 'male' in data and 'female' in data:
                complete_data[key] = data

        if not complete_data:
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, '没有完整的性别对比数据', ha='center', va='center', transform=ax.transAxes)
            return

        ax = self.figure.add_subplot(111)

        keys = list(complete_data.keys())
        male_values = [complete_data[key]['male'] for key in keys]
        female_values = [complete_data[key]['female'] for key in keys]

        x = np.arange(len(keys))
        width = 0.35

        ax.bar(x - width/2, male_values, width, label='男性', alpha=0.8)
        ax.bar(x + width/2, female_values, width, label='女性', alpha=0.8)

        ax.set_title('性别差异对比')
        ax.set_ylabel('目标值')
        ax.set_xticks(x)
        ax.set_xticklabels([k.replace('_', '\n') for k in keys], rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _plot_quality_distribution(self, targets: List[CalibrationTarget]):
        """绘制数据质量分布图"""
        quality_counts = {}
        for target in targets:
            quality = target.data_quality.value
            quality_counts[quality] = quality_counts.get(quality, 0) + 1

        if not quality_counts:
            return

        ax = self.figure.add_subplot(111)

        qualities = list(quality_counts.keys())
        counts = list(quality_counts.values())
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']

        wedges, texts, autotexts = ax.pie(counts, labels=qualities, colors=colors[:len(qualities)],
                                         autopct='%1.1f%%', startangle=90)

        ax.set_title('数据质量分布')

    def _plot_confidence_intervals(self, targets: List[CalibrationTarget]):
        """绘制置信区间图"""
        # 筛选有置信区间的目标
        ci_targets = [t for t in targets if t.confidence_interval is not None]

        if not ci_targets:
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, '没有置信区间数据', ha='center', va='center', transform=ax.transAxes)
            return

        ax = self.figure.add_subplot(111)

        # 按目标类型分组
        type_groups = {}
        for target in ci_targets:
            if target.target_type not in type_groups:
                type_groups[target.target_type] = []
            type_groups[target.target_type].append(target)

        y_pos = 0
        y_labels = []
        colors = plt.cm.Set3(np.linspace(0, 1, len(type_groups)))

        for i, (target_type, group_targets) in enumerate(type_groups.items()):
            for target in group_targets:
                lower, upper = target.confidence_interval
                center = target.value

                # 绘制置信区间
                ax.errorbar(center, y_pos, xerr=[[center - lower], [upper - center]],
                           fmt='o', color=colors[i], capsize=5, capthick=2)

                label = f"{target.age_group}_{target.gender}"
                y_labels.append(label)
                y_pos += 1

        ax.set_title('校准目标置信区间')
        ax.set_xlabel('目标值')
        ax.set_yticks(range(len(y_labels)))
        ax.set_yticklabels(y_labels)
        ax.grid(True, alpha=0.3)

    def _update_outputs_table(self):
        """更新模型输出表格"""
        self.outputs_table.setRowCount(len(self.current_model_outputs))
        self.outputs_table.setColumnCount(2)
        self.outputs_table.setHorizontalHeaderLabels(["目标键", "模型输出值"])

        for row, (key, value) in enumerate(self.current_model_outputs.items()):
            self.outputs_table.setItem(row, 0, QTableWidgetItem(key))
            self.outputs_table.setItem(row, 1, QTableWidgetItem(f"{value:.6f}"))

        self.outputs_table.resizeColumnsToContents()

    def _display_comparison_results(self, results: Dict[str, Any]):
        """显示比较结果"""
        result_text = "=== 校准目标比较分析结果 ===\n\n"

        # 总体拟合度
        overall_fit = results.get('overall_fit', {})
        if 'error' not in overall_fit:
            result_text += f"总体质量: {overall_fit.get('overall_quality', 'N/A')}\n"
            result_text += f"平均MAPE: {overall_fit.get('mean_mape', 0):.2f}%\n"
            result_text += f"平均相关系数: {overall_fit.get('mean_correlation', 0):.3f}\n"
            result_text += f"平均R²: {overall_fit.get('mean_r_squared', 0):.3f}\n\n"

        # 按目标类型的结果
        target_fits = results.get('target_specific_fit', {})
        for target_type, fit_data in target_fits.items():
            if 'error' in fit_data:
                result_text += f"{target_type}: {fit_data['error']}\n"
                continue

            metrics = fit_data.get('metrics', {})
            result_text += f"=== {target_type} ===\n"
            result_text += f"  拟合质量: {metrics.get('fit_quality', 'N/A')}\n"
            result_text += f"  WMAPE: {metrics.get('weighted_mape', 0):.2f}%\n"
            result_text += f"  相关系数: {metrics.get('correlation', 0):.3f}\n"
            result_text += f"  R²: {metrics.get('r_squared', 0):.3f}\n"
            result_text += f"  样本数: {fit_data.get('sample_size', 0)}\n\n"

        # 改进建议
        recommendations = results.get('recommendations', [])
        if recommendations:
            result_text += "=== 改进建议 ===\n"
            for i, rec in enumerate(recommendations, 1):
                result_text += f"{i}. {rec}\n"

        self.comparison_results.setText(result_text)

    def _on_table_item_changed(self, item):
        """处理表格项目变化"""
        # 这里可以实现表格编辑功能
        # 由于复杂性，暂时只记录日志
        logger.debug(f"表格项目变化: 行{item.row()}, 列{item.column()}, 值{item.text()}")

    def set_model_outputs(self, outputs: Dict[str, float]):
        """设置模型输出数据"""
        self.current_model_outputs = outputs.copy()
        self._update_outputs_table()
        self.status_bar.setText(f"设置了 {len(outputs)} 个模型输出")

    def get_targets_manager(self) -> CalibrationTargets:
        """获取目标管理器"""
        return self.targets

    def get_comparison_results(self) -> Optional[Dict[str, Any]]:
        """获取最新的比较结果"""
        if not self.current_model_outputs:
            return None

        try:
            return self.comparator.compare_outputs(self.current_model_outputs)
        except Exception as e:
            logger.error(f"获取比较结果失败: {e}")
            return None
