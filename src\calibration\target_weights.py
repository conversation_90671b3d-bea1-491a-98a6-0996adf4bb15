"""
校准目标权重管理模块
实现权重和优先级设置系统
"""

import json
import logging
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import numpy as np
from scipy import optimize

from .targets import CalibrationTarget, DataQuality, TargetType

logger = logging.getLogger(__name__)


@dataclass
class WeightScheme:
    """权重方案数据类"""
    name: str
    description: str
    weights: Dict[str, float]
    created_time: datetime
    author: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = asdict(self)
        result['created_time'] = self.created_time.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WeightScheme':
        """从字典创建对象"""
        data = data.copy()
        data['created_time'] = datetime.fromisoformat(data['created_time'])
        return cls(**data)


class TargetWeightManager:
    """校准目标权重管理器"""
    
    def __init__(self, storage_path: Optional[str] = None):
        """
        初始化权重管理器
        
        Args:
            storage_path: 存储路径
        """
        self.storage_path = Path(storage_path or "data/calibration_weights")
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 权重方案存储
        self.weight_schemes: Dict[str, WeightScheme] = {}
        self.current_scheme = "default"
        
        # 创建默认权重方案
        self._create_default_scheme()
        
        logger.info(f"权重管理器初始化完成，存储路径: {self.storage_path}")
    
    def _create_default_scheme(self) -> None:
        """创建默认权重方案"""
        default_weights = {
            'adenoma_prevalence': 1.0,
            'cancer_incidence': 2.0,
            'cancer_mortality': 3.0,
            'screening_detection': 1.5,
            'survival_rate': 2.5
        }
        
        self.weight_schemes["default"] = WeightScheme(
            name="default",
            description="默认权重方案",
            weights=default_weights,
            created_time=datetime.now(),
            author="system"
        )
    
    def create_weight_scheme(
        self, 
        scheme_name: str, 
        weights: Dict[str, float],
        description: str = "",
        author: str = ""
    ) -> None:
        """
        创建权重方案
        
        Args:
            scheme_name: 方案名称
            weights: 权重字典
            description: 描述
            author: 作者
        """
        self.weight_schemes[scheme_name] = WeightScheme(
            name=scheme_name,
            description=description,
            weights=weights.copy(),
            created_time=datetime.now(),
            author=author
        )
        
        logger.info(f"创建权重方案: {scheme_name}")
    
    def set_current_scheme(self, scheme_name: str) -> None:
        """
        设置当前权重方案
        
        Args:
            scheme_name: 方案名称
        """
        if scheme_name not in self.weight_schemes:
            raise ValueError(f"权重方案不存在: {scheme_name}")
        
        self.current_scheme = scheme_name
        logger.info(f"设置当前权重方案: {scheme_name}")
    
    def calculate_automatic_weights(
        self, 
        targets: List[CalibrationTarget]
    ) -> Dict[str, float]:
        """
        基于数据质量自动计算权重
        
        Args:
            targets: 校准目标列表
            
        Returns:
            目标键到权重的映射
        """
        weights = {}
        
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            
            # 基础权重
            base_weight = 1.0
            
            # 数据质量调整
            quality_multipliers = {
                DataQuality.HIGH: 2.0,
                DataQuality.MEDIUM: 1.0,
                DataQuality.LOW: 0.5,
                DataQuality.UNCERTAIN: 0.2
            }
            quality_weight = quality_multipliers.get(target.data_quality, 1.0)
            
            # 样本量调整
            if target.sample_size:
                # 对数缩放样本量权重
                sample_weight = np.log10(max(target.sample_size, 10)) / 4  # 标准化到合理范围
            else:
                sample_weight = 0.5  # 未知样本量的默认权重
            
            # 标准误差调整（标准误差越小，权重越高）
            if target.standard_error and target.standard_error > 0:
                se_weight = 1 / (1 + target.standard_error)
            else:
                se_weight = 1.0
            
            # 优先级调整
            priority_weight = 6 - target.priority  # 优先级1=权重5，优先级5=权重1
            
            # 目标类型基础权重
            type_base_weight = self.weight_schemes[self.current_scheme].weights.get(
                target.target_type.value, 1.0
            )
            
            # 综合权重
            final_weight = (base_weight * quality_weight * 
                          sample_weight * se_weight * priority_weight * type_base_weight)
            
            weights[target_key] = final_weight
        
        # 标准化权重（使总和为目标数量）
        total_weight = sum(weights.values())
        if total_weight > 0:
            normalization_factor = len(weights) / total_weight
            weights = {k: v * normalization_factor for k, v in weights.items()}
        
        logger.info(f"自动计算了 {len(weights)} 个目标的权重")
        return weights
    
    def optimize_weights(
        self, 
        targets: List[CalibrationTarget],
        model_outputs: Optional[Dict[str, float]] = None,
        optimization_method: str = "inverse_variance"
    ) -> Dict[str, float]:
        """
        优化权重以最小化校准误差
        
        Args:
            targets: 校准目标列表
            model_outputs: 模型输出值（可选）
            optimization_method: 优化方法
            
        Returns:
            优化后的权重
        """
        if optimization_method == "inverse_variance":
            return self._inverse_variance_weights(targets)
        elif optimization_method == "equal":
            return self._equal_weights(targets)
        elif optimization_method == "adaptive" and model_outputs:
            return self._adaptive_weights(targets, model_outputs)
        else:
            logger.warning(f"未知的优化方法或缺少模型输出: {optimization_method}")
            return self.calculate_automatic_weights(targets)
    
    def _inverse_variance_weights(self, targets: List[CalibrationTarget]) -> Dict[str, float]:
        """逆方差权重"""
        weights = {}
        
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            
            if target.standard_error and target.standard_error > 0:
                # 逆方差权重
                variance = target.standard_error ** 2
                weight = 1 / variance
            else:
                # 默认权重
                weight = 1.0
            
            weights[target_key] = weight
        
        # 标准化
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v / total_weight * len(weights) for k, v in weights.items()}
        
        return weights
    
    def _equal_weights(self, targets: List[CalibrationTarget]) -> Dict[str, float]:
        """等权重"""
        weights = {}
        equal_weight = 1.0
        
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            weights[target_key] = equal_weight
        
        return weights
    
    def _adaptive_weights(
        self, 
        targets: List[CalibrationTarget], 
        model_outputs: Dict[str, float]
    ) -> Dict[str, float]:
        """自适应权重（基于当前拟合误差）"""
        weights = {}
        
        # 计算每个目标的拟合误差
        errors = {}
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            
            if target_key in model_outputs:
                # 相对误差
                relative_error = abs(model_outputs[target_key] - target.value) / (target.value + 1e-8)
                errors[target_key] = relative_error
            else:
                errors[target_key] = 1.0  # 默认误差
        
        # 基于误差分配权重（误差大的权重高）
        max_error = max(errors.values()) if errors else 1.0
        
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            
            # 误差越大，权重越高
            error_weight = errors[target_key] / max_error
            
            # 结合数据质量
            quality_multipliers = {
                DataQuality.HIGH: 1.5,
                DataQuality.MEDIUM: 1.0,
                DataQuality.LOW: 0.7,
                DataQuality.UNCERTAIN: 0.3
            }
            quality_weight = quality_multipliers.get(target.data_quality, 1.0)
            
            weights[target_key] = error_weight * quality_weight
        
        # 标准化
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v / total_weight * len(weights) for k, v in weights.items()}
        
        return weights
    
    def perform_sensitivity_analysis(
        self, 
        targets: List[CalibrationTarget],
        weight_variations: List[float] = None
    ) -> Dict[str, Any]:
        """
        权重敏感性分析
        
        Args:
            targets: 校准目标列表
            weight_variations: 权重变化范围
            
        Returns:
            敏感性分析结果
        """
        if weight_variations is None:
            weight_variations = [0.5, 0.8, 1.0, 1.2, 1.5, 2.0]
        
        base_weights = self.calculate_automatic_weights(targets)
        sensitivity_results = {
            'base_weights': base_weights,
            'variations': {},
            'sensitivity_scores': {}
        }
        
        # 对每个目标进行敏感性分析
        for target in targets:
            target_key = f"{target.target_type.value}_{target.age_group}_{target.gender}"
            base_weight = base_weights.get(target_key, 1.0)
            
            variations = {}
            for variation in weight_variations:
                new_weight = base_weight * variation
                variations[str(variation)] = new_weight
            
            sensitivity_results['variations'][target_key] = variations
            
            # 计算敏感性分数（权重变化的标准差）
            weight_values = list(variations.values())
            sensitivity_score = np.std(weight_values) / np.mean(weight_values) if weight_values else 0
            sensitivity_results['sensitivity_scores'][target_key] = sensitivity_score
        
        logger.info(f"完成 {len(targets)} 个目标的敏感性分析")
        return sensitivity_results
    
    def save_schemes(self, file_path: Optional[str] = None) -> None:
        """保存权重方案"""
        if file_path is None:
            file_path = self.storage_path / "weight_schemes.json"
        else:
            file_path = Path(file_path)
        
        schemes_data = {
            name: scheme.to_dict() 
            for name, scheme in self.weight_schemes.items()
        }
        
        schemes_data['_metadata'] = {
            'current_scheme': self.current_scheme,
            'saved_time': datetime.now().isoformat()
        }
        
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(schemes_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"权重方案已保存到: {file_path}")
    
    def load_schemes(self, file_path: Union[str, Path]) -> None:
        """加载权重方案"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 加载方案
        self.weight_schemes.clear()
        for name, scheme_data in data.items():
            if name.startswith('_'):  # 跳过元数据
                continue
            self.weight_schemes[name] = WeightScheme.from_dict(scheme_data)
        
        # 加载元数据
        metadata = data.get('_metadata', {})
        if 'current_scheme' in metadata and metadata['current_scheme'] in self.weight_schemes:
            self.current_scheme = metadata['current_scheme']
        
        logger.info(f"加载了 {len(self.weight_schemes)} 个权重方案")
